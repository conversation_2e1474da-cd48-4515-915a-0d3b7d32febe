import type { Language } from 'element-plus/es/locale';

import type { App } from 'vue';

import type {
  LocaleSetupOptions,
  SupportedLanguagesType,
} from '@amdox/locales';

import { ref } from 'vue';

import TUILocaleMessages from '@amdox/chat-ui/locales';
import {
  $t,
  setupI18n as coreSetup,
  loadLocalesMapFromDir,
} from '@amdox/locales';
import { preferences } from '@amdox/preferences';

import dayjs from 'dayjs';
import enLocale from 'element-plus/es/locale/lang/en';
import defaultLocale from 'element-plus/es/locale/lang/zh-cn';

const elementLocale = ref<Language>(defaultLocale);

const modules = import.meta.glob('./langs/**/*.json');

const localesMap = loadLocalesMapFromDir(
  /\.\/langs\/([^/]+)\/(.*)\.json$/,
  modules,
);
/**
 * 加载应用特有的语言包
 * 这里也可以改造为从服务端获取翻译数据
 * @param lang
 */
async function loadMessages(lang: SupportedLanguagesType) {
  const [appLocaleMessages, _thirdPartyMessage, tuiLocaleMessages] =
    await Promise.all([
      localesMap[lang]?.(),
      loadThirdPartyMessage(lang),
      loadTUILocale(lang),
    ]);
  const messages = {
    ...appLocaleMessages?.default,
    ...tuiLocaleMessages,
  };
  return messages;
}

/**
 * 加载第三方组件库的语言包
 * @param lang
 */
async function loadThirdPartyMessage(lang: SupportedLanguagesType) {
  await Promise.all([loadElementLocale(lang), loadDayjsLocale(lang)]);
}

/**
 * 加载dayjs的语言包
 * @param lang
 */
async function loadDayjsLocale(lang: SupportedLanguagesType) {
  let locale;
  switch (lang) {
    case 'en-US': {
      locale = await import('dayjs/locale/en');
      break;
    }
    case 'zh-CN': {
      locale = await import('dayjs/locale/zh-cn');
      break;
    }
    // 默认使用英语
    default: {
      locale = await import('dayjs/locale/en');
    }
  }
  if (locale) {
    dayjs.locale(locale);
  } else {
    console.error(`Failed to load dayjs locale for ${lang}`);
  }
}

/**
 * 加载element-plus的语言包
 * @param lang
 */
async function loadElementLocale(lang: SupportedLanguagesType) {
  switch (lang) {
    case 'en-US': {
      elementLocale.value = enLocale;
      break;
    }
    case 'zh-CN': {
      elementLocale.value = defaultLocale;
      break;
    }
  }
}

/**
 * 加载TUI的语言包
 * @param lang
 * @returns
 */
async function loadTUILocale(lang: SupportedLanguagesType) {
  // eslint-disable-next-line no-console
  console.log('loadTUILocale lang', lang);
  // eslint-disable-next-line no-console
  console.log('loadTUILocale TUILocaleMessages', TUILocaleMessages[lang]);
  return {
    im: TUILocaleMessages[lang],
  };
}

async function setupI18n(app: App, options: LocaleSetupOptions = {}) {
  await coreSetup(app, {
    defaultLocale: preferences.app.locale,
    // @ts-ignore LoadMessageFn 已包含 ILanguageResources
    loadMessages,
    missingWarn: !import.meta.env.PROD,
    ...options,
  });
}

export { $t, elementLocale, setupI18n };
