# 删除群成员

## 接口信息

- **请求方法**：POST
- **接口地址**：https://test-web-im.amdox.com.cn/api/im/member/delGroupMember

#### 请求头部

（无额外参数）

#### 请求参数

| 字段      | 类型   | 是否必传 | 描述           |
| --------- | ------ | -------- | -------------- |
| groupId   | string | Yes      | 群编号         |
| reason    | string | No       | 删除原因       |
| imUserIds | array  | Yes      | 删除IM账号集合 |

#### 请求示例

```json
{
  "groupId": "10000",
  "reason": "",
  "imUserIds": ["CLASS_S_1_10011725"]
}
```

#### 响应参数

| 字段 | 类型 | 是否必传 | 描述 |
| ---- | ---- | -------- | ---- |

#### 响应示例

```json
null
```

#### 详细说明

- 接口描述：删除群成员接口，支持批量删除指定群内的IM用户。
