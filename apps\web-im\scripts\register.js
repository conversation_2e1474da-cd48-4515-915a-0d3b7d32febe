/* eslint-disable unicorn/no-process-exit */
/* eslint-disable n/prefer-global/process */
import axios from 'axios';

// ========== 配置区域 - 请根据实际情况修改 ==========

// API基础配置
const API_CONFIG = {
  // 基础URL - 请根据实际环境修改
  baseURL: 'https://test-web-im.amdox.com.cn',
  // 注册接口路径 - 根据接口文档调整
  registerPath: '/api/im/user/addIMUser',
  // 请求超时时间（毫秒）
  timeout: 10_000,
  // 请求头配置
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
    // 如果需要认证token，请在这里添加
    Authorization:
      'Bearer vbm3UNw6UeomBR62AW3ChvYVFQaubikh0tAZQKrcbQEtiP8z3RHuPv-e2UbuZ4rebLhmEDNAgf8ueDA2vvzxguZ-rWDlshhpovqGSvmD7WKPHH7gqjb1AIb0mx-4xwh9',
    // 如果需要学校ID，请在这里添加
    // 'campusId': 'your-school-id-here'
  },
};

// IM账号注册信息 - 请在这里填写要注册的账号信息
// 根据接口文档，参数格式为：imUserId, userName, userIconPath

// imUserId 测试环境：TEST + 角色 + 测试环境用户ID
// imUserId 生产环境：角色 + 生产环境用户ID
// 角色：TEACHER（教师）、STUDENT（学生）

// 注册之后可以直接放至环境变量内 VITE_MOCK_DATA_IM_AUTO_LOGIN_USER_ID 中
const REGISTER_ACCOUNTS = [
  // 测试环境
  {
    imUserId: 'TEST_TEACHER_11145963',
    userName: '刘泽林',
    userIconPath: 'http://picture.amdox.com.cn/static/defaultPhoto.png',
  },
  // 正式环境
  {
    imUserId: 'TEACHER_77650',
    userName: '刘泽林',
    userIconPath: 'http://picture.amdox.com.cn/static/defaultPhoto.png',
  },
];

// ========== 核心功能实现 ==========

/**
 * 创建HTTP客户端
 */
function createHttpClient() {
  const client = axios.create({
    baseURL: API_CONFIG.baseURL,
    timeout: API_CONFIG.timeout,
    headers: API_CONFIG.headers,
  });

  // 请求拦截器
  client.interceptors.request.use(
    (config) => {
      // 仅在开发环境输出详细日志
      if (process.env.NODE_ENV === 'development') {
        console.warn(
          `🚀 发送请求: ${config.method?.toUpperCase()} ${config.url}`,
        );
        console.warn(`📤 请求数据:`, JSON.stringify(config.data, null, 2));
      }
      return config;
    },
    (error) => {
      console.error('❌ 请求错误:', error.message);
      return Promise.reject(error);
    },
  );

  // 响应拦截器
  client.interceptors.response.use(
    (response) => {
      if (process.env.NODE_ENV === 'development') {
        console.warn(`✅ 响应成功: ${response.status} ${response.statusText}`);
        console.warn(`📥 响应数据:`, JSON.stringify(response.data, null, 2));
      }
      return response;
    },
    (error) => {
      console.error(
        '❌ 响应错误:',
        error.response?.status,
        error.response?.statusText,
      );
      console.error('📥 错误详情:', error.response?.data);
      return Promise.reject(error);
    },
  );

  return client;
}

/**
 * 批量注册IM账号
 * 根据接口文档，这个接口支持批量添加，所以直接发送数组
 * @param {Array} accounts 账号列表
 * @returns {Promise<object>} 注册结果
 */
async function batchRegisterIMAccounts(accounts) {
  const client = createHttpClient();

  try {
    // 根据接口文档，直接发送账号数组
    const response = await client.post(API_CONFIG.registerPath, accounts);

    // 处理响应结果
    const result = response.data;

    console.warn(`✅ 批量注册请求发送成功，共 ${accounts.length} 个账号`);
    console.warn('📥 服务器响应:', JSON.stringify(result, null, 2));

    return {
      success: true,
      data: result,
      count: accounts.length,
      message: '批量注册请求成功',
    };
  } catch (error) {
    console.error(`❌ 批量注册失败:`, error.message);
    if (error.response?.data) {
      console.error(
        '📥 错误详情:',
        JSON.stringify(error.response.data, null, 2),
      );
    }
    return {
      success: false,
      error: error.message,
      count: accounts.length,
      details: error.response?.data,
    };
  }
}

/**
 * 单个注册IM账号（如果需要逐个处理）
 * @param {object} accountInfo 账号信息
 * @returns {Promise<object>} 注册结果
 */
async function registerSingleIMAccount(accountInfo) {
  const client = createHttpClient();

  try {
    // 发送单个账号（作为数组）
    const response = await client.post(API_CONFIG.registerPath, [accountInfo]);

    const result = response.data;

    return {
      success: true,
      data: result,
      account: accountInfo.imUserId,
      message: '注册成功',
    };
  } catch (error) {
    console.error(`❌ 账号 ${accountInfo.imUserId} 注册异常:`, error.message);
    return {
      success: false,
      error: error.message,
      account: accountInfo.imUserId,
      details: error.response?.data,
    };
  }
}

/**
 * 逐个注册IM账号（如果批量接口有问题，可以尝试逐个注册）
 * @param {Array} accounts 账号列表
 * @returns {Promise<Array>} 注册结果列表
 */
async function registerAccountsOneByOne(accounts) {
  const results = [];

  for (let i = 0; i < accounts.length; i++) {
    const account = accounts[i];
    console.warn(
      `📝 正在注册第 ${i + 1}/${accounts.length} 个账号: ${account.imUserId}`,
    );

    try {
      const result = await registerSingleIMAccount(account);
      results.push(result);

      // 添加延迟避免请求过于频繁
      if (i < accounts.length - 1) {
        console.warn('⏳ 等待 1 秒后继续...');
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    } catch (error) {
      console.error(`❌ 账号 ${account.imUserId} 处理异常:`, error);
      results.push({
        success: false,
        error: error.message,
        account: account.imUserId,
      });
    }
  }

  return results;
}

/**
 * 打印注册结果统计
 * @param {object | Array} results 注册结果
 */
function printResultSummary(results) {
  const separator = '='.repeat(50);
  console.warn(`\n${separator}`);
  console.warn('📊 注册结果统计');
  console.warn(separator);

  if (Array.isArray(results)) {
    // 逐个注册的结果
    const successCount = results.filter((r) => r.success).length;
    const failCount = results.filter((r) => !r.success).length;

    console.warn(`✅ 成功: ${successCount} 个`);
    console.warn(`❌ 失败: ${failCount} 个`);
    console.warn(`📈 总计: ${results.length} 个`);

    if (failCount > 0) {
      console.warn('\n❌ 失败详情:');
      results
        .filter((r) => !r.success)
        .forEach((result, index) => {
          console.warn(`  ${index + 1}. ${result.account}: ${result.error}`);
        });
    }

    if (successCount > 0) {
      console.warn('\n✅ 成功列表:');
      results
        .filter((r) => r.success)
        .forEach((result, index) => {
          console.warn(`  ${index + 1}. ${result.account}`);
        });
    }
  } else {
    // 批量注册的结果
    console.warn(`📊 批量注册结果: ${results.success ? '成功' : '失败'}`);
    console.warn(`📈 处理账号数量: ${results.count} 个`);
    console.warn(`💬 消息: ${results.message || results.error}`);

    if (results.data) {
      console.warn('\n📥 服务器返回数据:');
      console.warn(JSON.stringify(results.data, null, 2));
    }

    if (results.details) {
      console.warn('\n❌ 错误详情:');
      console.warn(JSON.stringify(results.details, null, 2));
    }
  }
}

/**
 * 验证配置
 * @returns {boolean} 配置是否有效
 */
function validateConfig() {
  if (
    !API_CONFIG.baseURL ||
    API_CONFIG.baseURL === 'https://your-api-domain.com'
  ) {
    console.error('❌ 请先配置正确的API_CONFIG.baseURL');
    return false;
  }

  if (REGISTER_ACCOUNTS.length === 0) {
    console.error('❌ 请先在REGISTER_ACCOUNTS中配置要注册的账号信息');
    return false;
  }

  // 验证账号信息格式
  for (const account of REGISTER_ACCOUNTS) {
    if (!account.imUserId || !account.userName || !account.userIconPath) {
      console.error(
        '❌ 账号信息不完整，请确保每个账号都包含 imUserId、userName、userIconPath 字段',
      );
      console.error('❌ 问题账号:', JSON.stringify(account, null, 2));
      return false;
    }
  }

  return true;
}

/**
 * 主函数
 */
async function main() {
  console.warn('🎯 IM账号批量注册脚本启动');
  console.warn('='.repeat(50));

  // 验证配置
  if (!validateConfig()) {
    process.exit(1);
  }

  console.warn(`📋 配置信息:`);
  console.warn(`   API地址: ${API_CONFIG.baseURL}${API_CONFIG.registerPath}`);
  console.warn(`   账号数量: ${REGISTER_ACCOUNTS.length} 个`);
  console.warn(`   账号列表:`);
  REGISTER_ACCOUNTS.forEach((account, index) => {
    console.warn(
      `     ${index + 1}. ${account.imUserId} (${account.userName})`,
    );
  });

  try {
    console.warn('\n🔄 开始批量注册...');

    // 首先尝试批量注册
    const batchResult = await batchRegisterIMAccounts(REGISTER_ACCOUNTS);

    if (batchResult.success) {
      console.warn('✅ 批量注册成功!');
      printResultSummary(batchResult);
    } else {
      console.warn('❌ 批量注册失败，尝试逐个注册...');

      // 如果批量失败，尝试逐个注册
      const individualResults =
        await registerAccountsOneByOne(REGISTER_ACCOUNTS);
      printResultSummary(individualResults);

      // 根据结果设置退出码
      const hasFailures = individualResults.some((r) => !r.success);
      if (hasFailures) {
        process.exit(1);
      }
    }

    console.warn('\n🎉 脚本执行完成!');
  } catch (error) {
    console.error('❌ 脚本执行异常:', error);
    process.exit(1);
  }
}

// ========== 辅助功能 ==========

/**
 * 生成测试账号信息
 * @param {number} count 生成数量
 * @returns {Array} 用户信息列表
 */
function generateTestAccounts(count = 3) {
  const accounts = [];
  for (let i = 1; i <= count; i++) {
    accounts.push({
      imUserId: `TEST_USER_${i}`,
      userName: `测试用户${i}`,
      userIconPath: 'http://picture.amdox.com.cn/static/defaultPhoto.png',
    });
  }
  return accounts;
}

// 检查命令行参数
const args = process.argv.slice(2);
if (args.includes('--test')) {
  // 使用测试数据
  REGISTER_ACCOUNTS.length = 0;
  REGISTER_ACCOUNTS.push(...generateTestAccounts(3));
  console.warn('🧪 使用测试数据模式');
}

// 直接执行主函数
main().catch(console.error);
