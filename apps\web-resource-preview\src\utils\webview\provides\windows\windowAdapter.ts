import type { WebviewProvide } from '@amdox/webview';

import { PPTEventBus } from '#/utils/event-bus';

export interface ChunkInfo {
  key: string;
  fileName: string;
  mimeType?: string;
  chunkIndex: number;
  chunkData: string;
  totalChunks: number;
}

export class WindowsProvide implements WebviewProvide {
  /**
   * 强制刷新预览
   */
  public ppt_force_reload_preview = (): Promise<void> => {
    PPTEventBus.emitForceReloadPreview();
    return Promise.resolve();
  };
  /**
   * 跳转指定页
   */
  public ppt_jump_to_page = (page: number): Promise<void> => {
    PPTEventBus.emitJumpToPage(page);
    return Promise.resolve();
  };

  /**
   * 下一帧动画
   */
  public ppt_next_anim = (): Promise<void> => {
    PPTEventBus.emitNextAnim();
    return Promise.resolve();
  };

  /**
   * 下一页
   */
  public ppt_next_page = (): Promise<void> => {
    // eslint-disable-next-line no-console
    console.log('nextPage');
    // 发送下一页事件
    PPTEventBus.emitNextPage();
    return Promise.resolve();
  };
  /**
   * 上一帧动画
   */
  public ppt_prev_anim = (): Promise<void> => {
    PPTEventBus.emitPrevAnim();
    return Promise.resolve();
  };

  /**
   * 上一页
   */
  public ppt_prev_page = (): Promise<void> => {
    // eslint-disable-next-line no-console
    console.log('prevPage');
    // 发送上一页事件
    PPTEventBus.emitPrevPage();
    return Promise.resolve();
  };

  public ppt_preview_by_base64 = (chunkInfo: string): Promise<void> => {
    PPTEventBus.emitPreviewByBase64(JSON.parse(chunkInfo) as ChunkInfo);
    return Promise.resolve();
  };

  /*
   * 通过blob数据预览
   */
  public ppt_preview_by_blob = (
    fileName: string,
    blob: Blob,
  ): Promise<void> => {
    PPTEventBus.emitPreviewByBlob(fileName, blob);
    return Promise.resolve();
  };

  /**
   * 设置当前页码
   */
  public ppt_set_current_page = (pageIndex: number): Promise<void> => {
    // eslint-disable-next-line no-console
    console.log('setCurrentPage', pageIndex);
    // 发送跳转到指定页事件
    PPTEventBus.emitGoToPage(pageIndex);
    return Promise.resolve();
  };
}
