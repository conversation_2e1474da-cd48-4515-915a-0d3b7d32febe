@use '@amdox/chat-ui/assets/styles/common';
// button
@mixin btn {
  padding: 13px 0;
  cursor: pointer;
  border-radius: 5px;
}

@mixin btn-default {
  color: #fff;
  background: #006eff;
  border: 1px solid #006eff;

  @include btn;
}

@mixin btn-error {
  color: #e54545;
  background: #fff;
  border: 1px solid #e54545;

  @include btn;
}

@mixin btn-normal {
  color: #000;
  background: #fff;
  border: 1px solid #ddd;

  @include btn;
}

// flex
// flex布局 默认 纵向垂直居中水平居中
@mixin flex($direction: column, $js: center, $al: center) {
  box-sizing: border-box;
  display: -webkit-box;
  display: -moz-box;
  display: flex;
  display: -moz-flex;
  display: flexbox;
  display: flex;
  flex-direction: $direction;
  align-items: $al;
  justify-content: $js;
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0 solid black;
}

// 文本超出隐藏 ...隐藏文本
@mixin single-line-ellipsis($width: 100%) {
  width: $width;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 文本最多(n)行，超出部分用...表示
@mixin line($num) {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: $num;
  -webkit-box-orient: vertical;
}

// position居中
@mixin positionCenter {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.container {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 3;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: rgb(0 0 0 / 30%);

  @include flex;

  .box {
    overflow: hidden;
    color: #000;
    background: #fff;
    border-radius: 0.5rem;

    @include flex;
  }

  .box-h5 {
    justify-content: flex-start;
    width: 100%;
    height: 100%;
    padding: 0;
    border-radius: 0;

    .title {
      position: relative;
      box-sizing: border-box;
      width: 100%;
      padding: 15px 18px;

      @include flex;

      .title-back {
        position: absolute;
        left: 18px;
      }

      .title-name {
        font-family: PingFangSC-Medium;
        font-size: 18px;
        font-weight: 500;
      }
    }
  }
}
