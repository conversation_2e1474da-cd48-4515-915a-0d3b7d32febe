# @amdox/chat-ui components 目录结构

## Chat UI 组件目录结构

### components/

```
├── index.ts                    # 组件统一导出入口
├── common/                     # 通用组件库
│   ├── Avatar/                 # 头像组件
│   │   └── index.vue           # 支持自定义尺寸、圆角、骨架屏动画的头像组件，兼容Web和UniApp
│   ├── BottomPopup/           # 底部弹窗组件
│   │   ├── index.vue           # 移动端底部弹出对话框，PC端仅展示插槽内容
│   │   ├── index.ts            # 组件导出
│   │   └── style/              # 样式文件
│   ├── DatePicker/            # 日期选择器组件
│   │   ├── index.vue           # 主组件文件
│   │   ├── index.ts            # 组件导出
│   │   ├── date-table.vue      # 日期表格组件
│   │   ├── date-picker.ts      # 日期选择器类型定义
│   │   └── date-picker-panel.vue # 日期选择面板
│   ├── Dialog/                # 对话框组件
│   │   ├── index.vue           # 通用对话框，支持自定义头部、底部、背景等
│   │   ├── index.ts            # 组件导出
│   │   └── style/              # 样式文件
│   ├── Drag/                  # 拖拽组件
│   │   ├── index.vue           # 拖拽功能组件，仅适用于Web和H5
│   │   └── index.ts            # 组件导出
│   ├── Drawer/                # 抽屉组件
│   │   ├── index.vue           # 侧边抽屉组件
│   │   ├── index.ts            # 组件导出
│   │   └── style/              # 样式文件
│   ├── FetchMore/             # 加载更多组件
│   │   ├── index.vue           # 上拉加载更多功能组件
│   │   └── index.ts            # 组件导出
│   ├── Icon.vue               # 图标组件 - 通用图标显示组件
│   ├── ImagePreviewer/        # 图片预览组件
│   │   ├── index.vue           # 图片预览器，支持缩放、旋转、下载等功能
│   │   ├── index.ts            # 组件导出
│   │   └── image-item.vue      # 单个图片项组件
│   ├── Loading/               # 加载组件
│   │   ├── index.vue           # 旋转加载动画组件，支持自定义颜色和尺寸
│   │   └── index.ts            # 组件导出
│   ├── MaskLayer/             # 遮罩层组件
│   │   ├── index.vue           # 遮罩层组件
│   │   └── index.ts            # 组件导出
│   ├── Overlay/               # 覆盖层组件
│   │   ├── index.vue           # 覆盖层组件
│   │   └── index.ts            # 组件导出
│   ├── Popconfirm/            # 气泡确认框组件
│   │   ├── index.vue           # 气泡确认框组件
│   │   ├── index.ts            # 组件导出
│   │   └── style/              # 样式文件
│   ├── ProgressMessage/       # 进度消息组件
│   │   ├── index.vue           # 消息发送进度显示组件
│   │   └── index.ts            # 组件导出
│   ├── RadioSelect/           # 单选选择组件
│   │   ├── index.vue           # 单选选择器组件
│   │   ├── index.ts            # 组件导出
│   │   └── style/              # 样式文件
│   ├── SelectUser/            # 用户选择组件
│   │   ├── index.vue           # 用户选择器组件，用于选择聊天对象
│   │   └── index.ts            # 组件导出
│   ├── Slider/                # 滑块组件
│   │   └── index.vue           # 滑块组件，支持数值调节
│   ├── SwitchBar/             # 切换栏组件
│   │   ├── index.vue           # 切换栏组件
│   │   ├── index.ts            # 组件导出
│   │   └── style/              # 样式文件
│   ├── Toast/                 # 提示组件
│   │   ├── index.vue           # 消息提示组件，支持成功、错误、警告等类型
│   │   ├── index.ts            # Toast管理器，提供编程式调用
│   │   └── type.ts             # Toast类型定义
│   └── Transfer/              # 穿梭框组件
│       ├── index.vue           # 穿梭框组件，用于在两个列表间移动数据
│       ├── index.ts            # 组件导出
│       └── style/              # 样式文件
│
├── TUIChat/                   # 聊天主组件
│   ├── index.ts               # 组件导出
│   ├── index.vue              # 主组件文件 - 聊天界面容器，管理会话状态和组件协调
│   ├── config.ts              # 配置文件 - 聊天功能开关配置，支持20+功能特性控制
│   ├── server.ts              # 服务层 - 聊天相关业务逻辑处理
│   ├── style/                 # 样式文件
│   ├── utils/                 # 工具函数
│   │   ├── chatStorage.ts     # 聊天存储工具 - 本地存储管理
│   │   ├── conversationDraft.ts # 会话草稿管理 - 输入内容草稿保存/恢复
│   │   ├── convertVoiceToText.ts # 语音转文字工具
│   │   ├── copy.ts            # 复制功能工具 - 消息复制到剪贴板
│   │   ├── sendMessage.ts     # 发送消息工具 - 消息发送逻辑封装
│   │   ├── translation.ts     # 翻译功能工具 - 消息翻译服务
│   │   ├── utils.ts           # 通用工具函数
│   │   └── wordsList.ts       # 敏感词列表管理
│   ├── chat-header/           # 聊天头部组件
│   │   └── index.vue          # 聊天头部 - 显示会话名称、返回按钮、扩展功能入口
│   ├── emoji-config/          # 表情配置
│   │   ├── index.ts           # 表情配置主文件 - 表情解析和渲染逻辑
│   │   ├── default-emoji.ts   # 默认表情包配置
│   │   ├── custom-emoji.ts    # 自定义表情包配置
│   │   └── locales/           # 表情本地化配置
│   ├── forward/               # 转发功能
│   │   └── index.vue          # 消息转发组件 - 支持单条/合并转发
│   ├── message-input/         # 消息输入组件
│   │   ├── index.ts           # 组件导出
│   │   ├── index.vue          # 输入组件容器
│   │   ├── message-input-button.vue    # 发送按钮组件
│   │   ├── message-input-editor.vue    # 富文本编辑器 - 基于TipTap，支持@、表情、图片等
│   │   ├── message-input-file.ts       # 文件输入处理 - 图片/文件拖拽上传
│   │   ├── message-input-at/           # @功能组件
│   │   │   └── index.ts        # @用户提示和选择逻辑
│   │   └── message-input-quote/        # 引用功能组件
│   │       └── index.vue       # 消息引用/回复界面
│   ├── message-input-toolbar/ # 消息输入工具栏
│   │   ├── index.ts           # 组件导出
│   │   ├── index.vue          # 工具栏主组件 - 表情、文件、语音等功能入口
│   │   ├── style/             # 工具栏样式
│   │   ├── emoji-picker/      # 表情选择器
│   │   │   └── index.vue      # 表情面板组件
│   │   ├── evaluate/          # 评价功能
│   │   │   └── index.vue      # 消息评价组件
│   │   ├── file-upload/       # 文件上传
│   │   │   └── index.vue      # 文件选择和上传组件
│   │   ├── image-upload/      # 图片上传
│   │   │   └── index.vue      # 图片选择和上传组件
│   │   ├── video-upload/      # 视频上传
│   │   │   └── index.vue      # 视频选择和上传组件
│   │   ├── user-selector/     # 用户选择器
│   │   │   └── index.vue      # @用户选择组件
│   │   ├── words/             # 快捷回复
│   │   │   └── index.vue      # 快捷回复词组件
│   │   └── toolbar-item-container/ # 工具栏项容器
│   │       └── index.vue      # 工具栏项布局容器
│   ├── message-list/          # 消息列表组件
│   │   ├── index.vue          # 消息列表主组件 - 虚拟滚动、消息渲染、交互处理
│   │   ├── style/             # 消息列表样式
│   │   ├── link/              # 链接处理
│   │   │   └── index.vue      # 消息中链接识别和处理
│   │   ├── message-elements/  # 消息元素组件集合
│   │   │   ├── message-bubble.vue      # 消息气泡容器 - 消息布局和样式
│   │   │   ├── message-text.vue        # 文本消息组件
│   │   │   ├── message-image.vue       # 图片消息组件
│   │   │   ├── message-video.vue       # 视频消息组件
│   │   │   ├── message-audio.vue       # 语音消息组件
│   │   │   ├── message-file.vue        # 文件消息组件
│   │   │   ├── message-face.vue        # 表情消息组件
│   │   │   ├── message-location.vue    # 位置消息组件
│   │   │   ├── message-custom.vue      # 自定义消息组件
│   │   │   ├── message-tip.vue         # 提示消息组件
│   │   │   ├── message-timestamp.vue   # 时间戳组件
│   │   │   ├── message-quote/          # 引用消息组件
│   │   │   ├── message-record/         # 消息记录组件
│   │   │   ├── message-convert/        # 消息转换组件
│   │   │   ├── message-translate/      # 消息翻译组件
│   │   │   ├── message-stream-markdown/ # 流式Markdown消息组件
│   │   │   ├── read-status/            # 已读状态组件
│   │   │   └── simple-message-list/    # 简化消息列表组件
│   │   ├── message-group-application/ # 群组申请消息
│   │   │   └── index.vue      # 群组申请/邀请消息处理
│   │   ├── message-tool/      # 消息工具
│   │   │   └── index.vue      # 消息操作工具栏（复制、删除、转发等）
│   │   ├── read-receipt-panel/ # 已读回执面板
│   │   │   └── index.vue      # 消息已读状态详情面板
│   │   └── scroll-button/     # 滚动按钮
│   │       └── index.vue      # 快速滚动到底部按钮
│   ├── mulitple-select-panel/ # 多选面板
│   │   └── index.vue          # 消息多选操作面板 - 批量删除、转发等
│   └── offlinePushInfoManager/ # 离线推送信息管理
│       └── index.ts           # 离线推送配置和管理
│
├── TUIConversation/           # 会话列表组件
│   ├── index.ts               # 组件导出
│   ├── index.vue              # 主组件文件 - 会话列表容器，管理会话状态和组件协调
│   ├── server.ts              # 服务层 - 会话相关业务逻辑，创建会话、隐藏头部等功能
│   ├── style/                 # 样式文件
│   ├── conversation-header/   # 会话头部组件
│   │   ├── index.vue          # 会话头部 - 显示操作菜单，支持创建单聊/群聊等功能
│   │   ├── index.ts           # 组件导出
│   │   └── server.ts          # 头部服务层 - 菜单管理和扩展功能
│   ├── conversation-list/     # 会话列表组件
│   │   ├── index.vue          # 会话列表主组件 - 显示所有会话，支持长按操作、在线状态等
│   │   └── style/             # 会话列表样式
│   ├── conversation-network/  # 会话网络状态组件
│   │   ├── index.vue          # 网络状态提示 - 显示网络异常提示信息
│   │   └── index.ts           # 组件导出
│   └── actions-menu/          # 操作菜单组件
│       └── index.vue          # 会话操作菜单 - 删除、置顶、免打扰等操作
│
├── TUIContact/                # 联系人组件
│   ├── index.ts               # 组件导出
│   ├── index.vue              # 主组件文件 - 联系人主容器，管理显示模式和布局
│   ├── server.ts              # 服务层 - 联系人服务注册和好友选择处理
│   ├── utils/                 # 工具函数库
│   │   └── index.ts           # 联系人相关工具函数 - 信息处理、好友操作、群组管理等
│   ├── contact-info/          # 联系人信息组件
│   │   ├── index.vue          # 联系人详情 - 显示详细信息、编辑功能、操作按钮
│   │   ├── index.ts           # 组件导出
│   │   ├── contact-info-config.ts # 信息配置 - 展示项、按钮、编辑功能配置
│   │   └── style/             # 联系人信息样式
│   ├── contact-list/          # 联系人列表组件
│   │   ├── index.vue          # 联系人列表 - 好友、群组、申请、黑名单列表管理
│   │   ├── index.ts           # 组件导出
│   │   ├── contact-list-item/ # 列表项组件
│   │   └── style/             # 联系人列表样式
│   ├── contact-search/        # 联系人搜索组件
│   │   ├── index.vue          # 搜索功能 - 用户/群组搜索、添加好友入口
│   │   └── index.ts           # 组件导出
│   └── select-friend/         # 好友选择组件
│       ├── index.vue          # 好友选择器 - 单选/多选模式、搜索集成
│       └── index.ts           # 组件导出
│
├── TUIGroup/                  # 群组组件
│   ├── index.ts               # 组件导出
│   ├── index.vue              # 主组件文件 - 群组主容器，管理子组件显示状态
│   ├── server.ts              # 服务层 - 群组服务注册、扩展管理、页面跳转处理
│   ├── create-group/          # 群组创建组件
│   │   ├── index.vue          # 群组创建 - 类型选择、信息设置、创建流程
│   │   ├── index.ts           # 组件导出
│   │   ├── group-introduction/ # 群组类型介绍
│   │   │   ├── index.vue      # 类型选择组件
│   │   │   └── config.ts      # 群组类型配置 - 5种群组类型定义和说明
│   │   └── style/             # 创建群组样式
│   ├── manage-group/          # 群组管理组件
│   │   ├── index.vue          # 群组管理主界面 - 管理功能导航、权限控制
│   │   ├── index.ts           # 组件导出
│   │   ├── manage-profile.vue # 群组资料管理 - 基本信息编辑
│   │   ├── manage-member.vue  # 群成员管理 - 成员列表、权限、踢出、禁言
│   │   ├── manage-admin.vue   # 群管理员管理 - 管理员任免、权限分配
│   │   ├── manage-name.vue    # 群名称管理 - 名称编辑、验证
│   │   ├── manage-notification.vue # 群公告管理 - 公告编辑、发布
│   │   └── style/             # 群组管理样式
│   └── select-member/         # 群成员选择组件
│       ├── index.vue          # 成员选择器 - 单选/多选、搜索、分页加载
│       └── index.ts           # 组件导出
│
├── TUISearch/                 # 搜索组件
│   ├── index.ts               # 组件导出
│   ├── index.vue              # 主组件文件 - 搜索功能主容器，管理全局搜索和会话内搜索
│   ├── server.ts              # 服务层 - 搜索服务注册和业务逻辑处理
│   ├── type.ts                # 类型定义 - 搜索相关类型、接口、枚举定义
│   ├── utils.ts               # 工具函数 - 搜索相关工具函数和辅助方法
│   ├── search-type-list.ts    # 搜索类型列表 - 定义搜索消息类型配置（全部、文本、文件等）
│   ├── search-time-list.ts    # 搜索时间列表 - 定义时间筛选配置（全部、今天、近三天等）
│   ├── style/                 # 样式文件
│   ├── search-container/      # 搜索容器组件
│   │   ├── index.vue          # 搜索容器主组件 - 搜索界面布局容器，管理搜索配置面板
│   │   ├── index.ts           # 组件导出
│   │   └── style/             # 搜索容器样式
│   ├── search-input/          # 搜索输入组件
│   │   ├── index.vue          # 搜索输入框 - 关键词输入、搜索触发、状态管理
│   │   └── index.ts           # 组件导出
│   ├── search-more/           # 搜索更多组件
│   │   ├── index.vue          # 搜索扩展功能 - 更多搜索选项和扩展功能入口
│   │   └── index.ts           # 组件导出
│   └── search-result/         # 搜索结果组件
│       ├── index.vue          # 搜索结果主组件 - 搜索结果展示、分页加载、结果处理
│       ├── index.ts           # 组件导出
│       ├── style/             # 搜索结果样式
│       ├── search-result-item/ # 搜索结果项组件
│       │   ├── index.vue      # 单个搜索结果项 - 结果项展示、点击处理、导航功能
│       │   ├── style/         # 结果项样式
│       │   └── message-abstract/ # 消息摘要组件集合
│       │       ├── message-abstract-text.vue    # 文本消息摘要 - 文本内容高亮显示
│       │       ├── message-abstract-image.vue   # 图片消息摘要 - 图片消息预览
│       │       ├── message-abstract-video.vue   # 视频消息摘要 - 视频消息预览
│       │       ├── message-abstract-file.vue    # 文件消息摘要 - 文件信息展示
│       │       └── message-abstract-custom.vue  # 自定义消息摘要 - 自定义消息处理
│       ├── search-result-loading/ # 搜索加载组件
│       │   ├── index.vue      # 搜索加载状态 - 搜索过程中的加载动画
│       │   └── index.ts       # 组件导出
│       └── search-result-default/ # 搜索默认状态组件
│           ├── index.vue      # 搜索默认界面 - 无搜索结果时的默认提示
│           └── index.ts       # 组件导出
│
└── TUINotification/           # 通知组件
    ├── index.ts               # 主要逻辑文件 - 浏览器原生通知管理器，单例模式设计，支持聊天和通话通知
    ├── interface.ts           # 接口定义 - 通知配置接口和类型定义（ITUINotification、NotificationType等）
    └── utils.ts               # 工具函数 - JSON安全解析和消息类型判断工具
```

## 组件功能说明

### 🔧 **common/** - 通用组件库

提供聊天应用中常用的基础UI组件，如头像、对话框、加载器、图片预览等。

### 💬 **TUIChat/** - 聊天主组件

核心聊天功能组件，包含：

- 消息列表展示和渲染
- 消息输入和编辑
- 表情、@功能、引用等
- 消息转发和多选操作

#### 🏗️ **架构设计**

- **index.vue** - 聊天界面主容器

  - 管理会话状态切换和组件协调
  - 处理群组状态和权限控制
  - 支持多选模式和工具栏显示控制
  - 集成TUICore事件通知机制

- **config.ts** - 功能配置管理器
  - 支持20+聊天功能特性开关控制
  - 包括：复制消息、删除消息、文件下载、表情回应、转发消息等
  - 支持主题配置和聊天类型设置
  - 提供hideTUIChatFeatures方法批量禁用功能

#### 🔧 **核心功能模块**

##### 📝 **消息输入系统 (message-input/)**

- **message-input-editor.vue** - 富文本编辑器

  - 基于TipTap构建，支持富文本编辑
  - 集成@用户功能和提示系统
  - 支持表情、图片、文件拖拽上传
  - 草稿自动保存和恢复机制
  - 移动端键盘适配和输入优化

- **message-input-button.vue** - 发送按钮

  - 智能发送状态控制
  - 支持快捷键发送（Enter）
  - 发送前内容验证

- **message-input-at/** - @功能

  - 用户搜索和选择
  - @提示面板显示
  - 群成员列表集成

- **message-input-quote/** - 引用回复
  - 消息引用界面
  - 支持引用和回复两种模式
  - 引用内容预览和取消

##### 🛠️ **输入工具栏 (message-input-toolbar/)**

- **emoji-picker/** - 表情选择器

  - 默认表情包和自定义表情
  - 表情分类和搜索
  - 最近使用表情记录

- **file-upload/** - 文件上传

  - 支持多种文件格式
  - 文件大小和类型验证
  - 上传进度显示

- **image-upload/** - 图片上传

  - 图片选择和预览
  - 图片压缩和格式转换
  - 支持多图上传

- **video-upload/** - 视频上传

  - 视频文件选择
  - 视频预览和信息显示
  - 上传进度和状态管理

- **user-selector/** - 用户选择器

  - @用户选择界面
  - 群成员搜索和过滤
  - 多用户选择支持

- **words/** - 快捷回复

  - 预设回复词管理
  - 快速插入常用语句
  - 自定义快捷回复

- **evaluate/** - 消息评价
  - 消息质量评价功能
  - 评价结果统计
  - 用户反馈收集

##### 📋 **消息列表系统 (message-list/)**

- **index.vue** - 消息列表主组件

  - 虚拟滚动优化，支持大量消息
  - 消息渲染和交互处理
  - 滚动位置管理和自动滚动
  - 消息选择和批量操作

- **message-elements/** - 消息类型组件

  - **message-bubble.vue** - 消息气泡容器
    - 统一消息布局和样式
    - 发送者信息显示
    - 消息状态指示器
  - **message-text.vue** - 文本消息
    - 富文本内容渲染
    - 链接识别和跳转
    - 表情解析和显示
  - **message-image.vue** - 图片消息
    - 图片懒加载和预览
    - 点击放大查看
    - 图片下载功能
  - **message-video.vue** - 视频消息
    - 视频播放控制
    - 封面图显示
    - 视频下载功能
  - **message-audio.vue** - 语音消息
    - 语音播放控制
    - 播放进度显示
    - 语音转文字功能
  - **message-file.vue** - 文件消息
    - 文件信息显示
    - 文件下载功能
    - 文件类型图标
  - **message-custom.vue** - 自定义消息
    - 扩展消息类型支持
    - 自定义渲染逻辑
    - 插件化消息处理

- **message-tool/** - 消息操作工具

  - 消息右键菜单
  - 复制、删除、转发、引用等操作
  - 权限控制和状态管理

- **read-receipt-panel/** - 已读回执

  - 消息已读状态详情
  - 已读用户列表显示
  - 已读时间统计

- **scroll-button/** - 滚动控制
  - 快速滚动到底部
  - 新消息提示
  - 滚动位置记忆

##### 🔄 **高级功能**

- **forward/** - 消息转发

  - 单条消息转发
  - 合并转发功能
  - 转发目标选择

- **mulitple-select-panel/** - 多选操作

  - 消息批量选择
  - 批量删除和转发
  - 选择状态管理

- **emoji-config/** - 表情系统
  - 表情包配置和管理
  - 表情解析和渲染
  - 自定义表情支持
  - 多语言表情本地化

##### 🛠️ **工具函数库 (utils/)**

- **conversationDraft.ts** - 草稿管理

  - 输入内容自动保存
  - 会话切换时草稿恢复
  - 草稿摘要生成

- **sendMessage.ts** - 消息发送

  - 消息发送逻辑封装
  - 发送状态管理
  - 错误处理和重试

- **translation.ts** - 翻译功能

  - 消息内容翻译
  - 多语言支持
  - 翻译结果缓存

- **copy.ts** - 复制功能

  - 消息内容复制
  - 富文本格式保持
  - 跨平台兼容

- **convertVoiceToText.ts** - 语音转文字
  - 语音识别服务集成
  - 转换结果处理
  - 错误处理机制

#### 🎯 **特色功能**

1. **智能输入** - 支持@提示、表情解析、草稿保存
2. **富媒体支持** - 图片、视频、语音、文件等多种消息类型
3. **高性能渲染** - 虚拟滚动优化，支持大量消息
4. **跨平台兼容** - Web、H5、UniApp全平台支持
5. **功能可配置** - 20+功能特性可独立开关
6. **扩展性强** - 支持自定义消息类型和插件扩展

### 📋 **TUIConversation/** - 会话列表组件

管理和展示用户的会话列表，包含会话操作菜单和网络状态显示。

#### 🏗️ **架构设计**

- **index.vue** - 会话列表主容器

  - 管理会话总未读数统计
  - 控制会话头部显示/隐藏
  - 协调各子组件交互
  - 监听TUIStore状态变化

- **server.ts** - 会话服务层
  - 注册TUICore服务和扩展
  - 处理创建会话业务逻辑
  - 管理单聊/群聊创建流程
  - 提供搜索扩展功能
  - 路由跳转和页面管理

#### 🔧 **核心功能模块**

##### 📋 **会话头部 (conversation-header/)**

- **index.vue** - 会话头部主组件

  - 显示操作菜单列表
  - 支持菜单项展开/收起
  - 处理菜单点击事件
  - 支持子菜单显示

- **server.ts** - 头部服务层
  - 菜单配置管理
  - 扩展功能注册
  - 创建单聊/群聊入口
  - 与TUICore服务集成

##### 📝 **会话列表 (conversation-list/)**

- **index.vue** - 会话列表主组件
  - 会话列表渲染和展示
  - 支持长按操作菜单
  - 在线状态显示
  - 未读消息数量显示
  - 置顶会话标识
  - 免打扰状态显示
  - 草稿消息提示
  - @消息提醒
  - 最后消息预览
  - 会话切换处理
  - 跨平台兼容（Web/H5/UniApp）

**核心功能特性：**

- **智能交互** - 支持点击进入、长按操作、右键菜单
- **状态管理** - 实时同步会话状态、未读数、在线状态
- **视觉反馈** - 选中状态、置顶标识、免打扰图标
- **消息预览** - 最后消息内容、时间、草稿提示
- **响应式设计** - 适配PC和移动端不同交互方式

##### 🌐 **网络状态 (conversation-network/)**

- **index.vue** - 网络状态提示组件
  - 监听网络连接状态
  - 显示网络异常提示
  - 实时状态更新
  - 用户友好的错误提示

##### ⚙️ **操作菜单 (actions-menu/)**

- **index.vue** - 会话操作菜单组件
  - 删除会话功能
  - 置顶/取消置顶会话
  - 消息免打扰/取消免打扰
  - 智能菜单定位
  - 边界检测和自适应
  - 确认对话框集成
  - 跨平台兼容处理

**菜单功能：**

- **删除会话** - 清空聊天记录，支持确认对话框
- **置顶管理** - 会话置顶/取消置顶
- **免打扰设置** - 消息免打扰开关
- **智能定位** - 自动检测屏幕边界，调整菜单位置
- **平台适配** - PC鼠标右键、移动端长按触发

#### 🎯 **特色功能**

1. **统一状态管理** - 通过TUIStore实现状态同步
2. **服务化架构** - 通过TUICore服务注册实现模块解耦
3. **扩展性强** - 支持搜索扩展和自定义菜单
4. **跨平台兼容** - Web、H5、UniApp全平台支持
5. **智能交互** - 长按、右键、点击等多种交互方式
6. **实时更新** - 网络状态、在线状态、消息状态实时同步

### 👥 **TUIContact/** - 联系人组件

联系人管理功能，包含联系人列表、搜索、信息展示和好友选择。

#### 🏗️ **架构设计**

- **index.vue** - 联系人主容器

  - 管理显示模式切换（联系人列表/好友选择）
  - 控制左右布局和响应式显示
  - 协调各子组件交互
  - 处理会话切换事件

- **server.ts** - 联系人服务层
  - 注册TUICore服务
  - 处理好友选择服务调用
  - 管理页面跳转和状态更新
  - 提供服务回调机制

#### 🔧 **核心功能模块**

##### 📋 **联系人信息 (contact-info/)**

- **index.vue** - 联系人详情主组件

  - 显示联系人/群组详细信息
  - 支持编辑备注、个性签名等
  - 提供操作按钮（发消息、加好友、删除等）
  - 处理好友申请接受/拒绝
  - 支持黑名单管理
  - 群组管理功能（解散、退出、加入）

- **contact-info-config.ts** - 联系人信息配置
  - 定义信息展示项配置
  - 按钮操作配置
  - 编辑功能配置
  - 权限控制逻辑

**核心功能特性：**

- **信息展示** - 头像、昵称、ID、个性签名等基本信息
- **状态管理** - 好友关系、黑名单状态、群组成员状态
- **操作功能** - 发消息、加好友、删除好友、拉黑等
- **编辑功能** - 备注修改、个性签名编辑
- **权限控制** - 根据关系状态显示不同操作选项

##### 📝 **联系人列表 (contact-list/)**

- **index.vue** - 联系人列表主组件

  - 显示好友列表、群组列表
  - 新的联系人（好友申请）
  - 黑名单管理
  - 支持在线状态显示
  - 搜索结果展示
  - 列表项点击选择
  - 未读数量提示

- **contact-list-item/** - 联系人列表项组件
  - 单个联系人/群组项渲染
  - 头像、昵称、状态显示
  - 支持在线状态指示器

**列表功能：**

- **分类管理** - 好友、群组、申请、黑名单分类
- **状态显示** - 在线状态、未读申请数量
- **交互操作** - 点击查看详情、展开/收起列表
- **搜索集成** - 搜索结果与列表数据整合

##### 🔍 **联系人搜索 (contact-search/)**

- **index.vue** - 搜索功能组件
  - 添加好友/群聊入口
  - 用户ID/群ID搜索
  - 搜索结果展示
  - 搜索状态管理
  - 防抖优化

**搜索功能：**

- **用户搜索** - 通过用户ID搜索用户
- **群组搜索** - 通过群ID搜索群组
- **状态切换** - 搜索模式与普通模式切换
- **结果处理** - 搜索结果统一管理和展示

##### 👥 **好友选择 (select-friend/)**

- **index.vue** - 好友选择器组件
  - 好友列表选择界面
  - 支持单选/多选模式
  - 集成搜索功能
  - 选择结果回调处理
  - 与SelectUser通用组件集成

**选择功能：**

- **模式支持** - 单选/多选模式
- **搜索集成** - 支持搜索添加非好友用户
- **结果处理** - 选择完成后回调处理
- **状态管理** - 选择状态和显示控制

##### 🛠️ **工具函数库 (utils/)**

- **index.ts** - 联系人相关工具函数
  - 头像生成和处理
  - 昵称显示逻辑
  - 好友关系检查
  - 备注修改功能
  - 好友添加/删除
  - 好友申请处理
  - 群组操作（加入、退出、解散）
  - 黑名单管理
  - 会话跳转处理

**工具函数分类：**

- **信息处理** - generateAvatar、generateName、generateContactInfoName
- **关系检查** - isFriend、isApplicationType
- **好友操作** - addFriend、deleteFriend、updateFriendRemark
- **申请处理** - acceptFriendApplication、refuseFriendApplication
- **群组操作** - joinGroup、quitGroup、dismissGroup
- **黑名单** - addToBlacklist、removeFromBlacklist
- **会话操作** - enterConversation

#### 🎯 **特色功能**

1. **统一信息管理** - 好友、群组、申请、黑名单统一管理
2. **智能状态显示** - 在线状态、关系状态、权限状态
3. **灵活操作模式** - 支持查看模式和选择模式
4. **完整的好友流程** - 搜索、申请、接受、管理全流程
5. **群组管理** - 加入、退出、解散等群组操作
6. **跨平台兼容** - Web、H5、UniApp全平台支持
7. **服务化架构** - 通过TUICore服务实现模块解耦

### 👨‍👩‍👧‍👦 **TUIGroup/** - 群组组件

群组相关功能，包含群组创建、管理和成员选择。

#### 🏗️ **架构设计**

- **index.vue** - 群组主容器

  - 管理三个核心子组件的显示状态
  - 通过TUIStore监听组件显示控制
  - 提供统一的群组功能入口

- **server.ts** - 群组服务层
  - 注册TUICore服务和扩展
  - 处理群组创建、管理、成员选择服务调用
  - 提供聊天头部扩展（群管理入口）
  - 管理页面跳转和状态更新
  - 服务回调机制管理

#### 🔧 **核心功能模块**

##### 🆕 **群组创建 (create-group/)**

- **index.vue** - 群组创建主组件

  - 群组类型选择界面
  - 群组基本信息设置（群名、群ID）
  - 成员列表展示
  - 群组创建流程处理
  - 编辑模式支持

- **group-introduction/** - 群组类型介绍
  - **index.vue** - 群组类型选择组件
  - **config.ts** - 群组类型配置
    - 5种群组类型定义（Public、Meeting、Work、AVChatroom、Community）
    - 每种类型的详细说明和图标
    - 群组特性和适用场景介绍

**创建功能特性：**

- **类型选择** - 支持5种不同类型的群组创建
- **信息设置** - 群名称、群ID（可选）、群头像设置
- **成员管理** - 显示初始成员列表
- **智能验证** - 群名称必填验证
- **类型适配** - 不同群组类型的特殊处理（如社群不需要群ID）

##### ⚙️ **群组管理 (manage-group/)**

- **index.vue** - 群组管理主组件

  - 群组信息总览
  - 管理功能导航
  - 权限控制逻辑
  - 成员操作处理
  - 群组设置管理

- **manage-profile.vue** - 群组资料管理

  - 群组基本信息编辑
  - 群头像、群名称修改
  - 群介绍、群公告设置

- **manage-member.vue** - 群成员管理

  - 成员列表展示
  - 成员权限管理
  - 成员踢出功能
  - 成员禁言设置

- **manage-admin.vue** - 群管理员管理

  - 管理员任免
  - 权限分配
  - 管理员列表维护

- **manage-name.vue** - 群名称管理

  - 群名称编辑
  - 名称验证
  - 修改历史

- **manage-notification.vue** - 群公告管理
  - 群公告编辑
  - 公告发布
  - 公告历史

**管理功能特性：**

- **权限控制** - 根据用户角色（群主/管理员/成员）显示不同功能
- **成员分类** - 管理员、普通成员、禁言成员分类管理
- **批量操作** - 支持批量成员操作
- **实时更新** - 群组信息实时同步
- **操作确认** - 重要操作提供确认对话框

##### 👥 **成员选择 (select-member/)**

- **index.vue** - 群成员选择组件
  - 群成员列表展示
  - 支持单选/多选模式
  - 集成搜索功能
  - 分页加载支持
  - 成员过滤功能
  - 选择结果处理

**选择功能特性：**

- **模式支持** - 单选/多选模式
- **搜索集成** - 支持群成员搜索
- **分页加载** - 大群成员分页获取
- **状态过滤** - 支持过滤特定用户
- **禁用控制** - 支持禁用特定成员选择

#### 🎯 **特色功能**

1. **完整群组生命周期** - 创建、管理、成员操作全流程
2. **多类型群组支持** - 5种不同类型群组，满足不同场景需求
3. **精细权限控制** - 基于角色的功能权限管理
4. **智能成员管理** - 分类管理、批量操作、搜索过滤
5. **服务化架构** - 通过TUICore服务实现模块解耦
6. **扩展性强** - 支持聊天头部扩展和自定义功能
7. **跨平台兼容** - Web、H5、UniApp全平台支持
8. **实时同步** - 群组状态和成员信息实时更新

### 🔍 **TUISearch/** - 搜索组件

全局搜索功能，支持按类型、时间等条件搜索消息和联系人。

#### 🏗️ **架构设计**

- **index.vue** - 搜索功能主容器

  - 管理全局搜索和会话内搜索两种模式
  - 控制搜索界面的显示状态和布局
  - 处理PC端和移动端的不同交互方式
  - 集成TUIStore状态管理和事件监听

- **server.ts** - 搜索服务层
  - 注册TUICore搜索服务
  - 处理搜索业务逻辑
  - 管理搜索扩展功能
  - 提供搜索API接口

#### 🔧 **核心功能模块**

##### 📝 **搜索输入系统 (search-input/)**

- **index.vue** - 搜索输入框组件
  - 关键词输入和实时搜索
  - 搜索状态管理和切换
  - 支持回车键搜索
  - 搜索历史和建议
  - 移动端键盘适配
  - 防抖优化搜索性能

**核心功能特性：**

- **智能输入** - 实时搜索建议、关键词高亮
- **状态管理** - 搜索中、搜索完成、清空搜索状态
- **跨平台适配** - PC端和移动端不同的交互体验
- **性能优化** - 防抖处理、避免重复搜索

##### 🏗️ **搜索容器系统 (search-container/)**

- **index.vue** - 搜索界面布局容器
  - 搜索配置面板管理
  - 搜索类型选择（全部、文本、文件、图片/视频、其他）
  - 时间筛选配置（全部、今天、近三天、近七天、自定义）
  - 日期选择器集成
  - 搜索条件实时更新

**配置功能特性：**

- **类型筛选** - 支持5种消息类型筛选
- **时间筛选** - 支持4种预设时间范围 + 自定义时间
- **界面适配** - 支持底部弹出和侧边显示两种布局
- **实时更新** - 搜索条件变更实时生效

##### 🔍 **搜索结果系统 (search-result/)**

- **index.vue** - 搜索结果主组件

  - 搜索结果展示和分页加载
  - 支持全局搜索和会话内搜索
  - 结果分类显示（联系人、群组、消息）
  - 搜索结果高亮显示
  - 无限滚动加载更多
  - 搜索结果统计

- **search-result-item/** - 搜索结果项组件

  - **index.vue** - 单个搜索结果项
    - 支持多种显示模式（信息流、气泡、文件、图片）
    - 结果项点击处理和导航
    - 悬停效果和交互反馈
    - 定位到聊天位置功能

- **message-abstract/** - 消息摘要组件集合

  - **message-abstract-text.vue** - 文本消息摘要
    - 文本内容高亮显示
    - 关键词匹配标记
    - 表情解析和显示
    - 支持字体高亮和背景高亮两种模式
  - **message-abstract-file.vue** - 文件消息摘要
    - 文件名称和大小显示
    - 文件类型图标
    - 文件下载功能
    - 文件信息预览
  - **message-abstract-image.vue** - 图片消息摘要
    - 图片缩略图显示
    - 图片预览功能
    - 图片信息展示
  - **message-abstract-video.vue** - 视频消息摘要
    - 视频封面显示
    - 视频时长信息
    - 视频播放入口
  - **message-abstract-custom.vue** - 自定义消息摘要
    - 自定义消息类型处理
    - 扩展消息格式支持
    - 插件化消息渲染

- **search-result-loading/** - 搜索加载状态

  - 搜索过程中的加载动画
  - 加载状态提示
  - 搜索进度反馈

- **search-result-default/** - 搜索默认状态
  - 无搜索结果时的提示界面
  - 搜索建议和帮助信息
  - 空状态友好提示

##### ⚙️ **搜索扩展系统 (search-more/)**

- **index.vue** - 搜索扩展功能组件
  - 更多搜索选项入口
  - 扩展功能菜单
  - 高级搜索配置
  - 搜索设置管理
  - 与TUICore扩展系统集成

##### 🛠️ **配置和工具**

- **search-type-list.ts** - 搜索类型配置

  - 定义5种搜索消息类型（全部、文本、文件、图片/视频、其他）
  - 全局搜索和会话内搜索的类型差异配置
  - 消息类型映射和过滤规则

- **search-time-list.ts** - 时间筛选配置

  - 定义4种预设时间范围（全部、今天、近三天、近七天）
  - 时间计算和转换逻辑
  - 自定义时间范围支持

- **type.ts** - 类型定义系统

  - 搜索相关接口和类型定义
  - 搜索结果数据结构
  - 搜索状态和配置类型
  - 消息摘要显示类型枚举

- **utils.ts** - 搜索工具函数库
  - 搜索API调用封装
  - 搜索结果处理和格式化
  - 关键词高亮处理
  - 搜索防抖和性能优化
  - 搜索结果导航和跳转

#### 🎯 **特色功能**

1. **双模式搜索** - 支持全局搜索和会话内搜索两种模式
2. **多维度筛选** - 按消息类型、时间范围、关键词等多维度筛选
3. **智能高亮** - 搜索关键词智能高亮显示
4. **多种展示模式** - 信息流、气泡、文件、图片等多种结果展示模式
5. **无限滚动** - 搜索结果分页加载，支持大量数据展示
6. **跨平台适配** - PC端和移动端不同的交互体验
7. **扩展性强** - 支持自定义搜索类型和扩展功能
8. **性能优化** - 防抖搜索、虚拟滚动、结果缓存等优化
9. **实时导航** - 支持从搜索结果直接定位到聊天位置
10. **状态管理** - 完整的搜索状态管理和持久化

### 🔔 **TUINotification/** - 通知组件

处理各种通知逻辑，以纯逻辑组件形式存在。

#### 🏗️ **架构设计**

- **单例模式** - 全局唯一实例管理，通过getInstance()获取
- **纯逻辑组件** - 无UI界面，专注于通知业务逻辑处理
- **Web专用** - 基于浏览器原生Notification API实现
- **异步处理** - 完全异步化设计，不阻塞主线程

#### 🔧 **核心功能**

##### 📋 **通知类型支持**

- **聊天通知 (chat)**

  - 支持9种消息类型：文本、图片、视频、语音、文件、表情、位置、聊天记录、自定义
  - 智能内容生成：显示发送者、会话名称、消息预览
  - 隐私保护：支持关闭消息预览，仅显示未读数量
  - 会话区分：单聊显示用户昵称，群聊显示群名称

- **通话通知 (call)**
  - 音视频通话邀请通知
  - 通话取消通知
  - 持续性通知：通话邀请时保持显示
  - 通话状态识别：区分开始、结束、取消状态

##### 🛠️ **智能过滤机制**

- **消息过滤条件**

  - 排除已撤回、已删除消息
  - 排除正在输入状态消息
  - 排除群提示和群系统通知
  - 排除当前聚焦页面的当前会话消息
  - 排除无效消息（无ID、无类型等）

- **环境检测**
  - 浏览器通知能力检测
  - 页面焦点状态检测
  - 通知权限状态检测

##### ⚙️ **配置系统**

- **allowNotifications** - 通知总开关
- **showPreviews** - 消息预览开关
- **notificationTitle** - 通知标题（默认：腾讯云 IM）
- **notificationIcon** - 通知图标URL

##### 🔄 **交互处理**

- **点击响应** - 点击通知自动聚焦窗口并切换到对应会话
- **自动关闭** - 点击后自动关闭通知
- **会话跳转** - 支持单聊、群聊会话的自动切换

#### 🎯 **特色功能**

1. **智能通知过滤** - 多维度过滤，避免无效通知干扰
2. **双类型支持** - 聊天消息和音视频通话通知分别处理
3. **隐私保护** - 支持关闭消息预览，保护用户隐私
4. **权限友好** - 自动处理浏览器权限请求和状态管理
5. **交互优化** - 点击通知直接跳转到对应会话
6. **配置灵活** - 支持动态配置通知行为和显示内容
7. **错误容错** - 完善的错误处理和降级机制
