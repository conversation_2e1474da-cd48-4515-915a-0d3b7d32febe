# Monorepo 包实现指南

本文档旨在说明如何在 `teaching-magic-cube-tool` Monorepo 项目中规范地实现一个独立的包（模块）。这些包通常位于 `packages/` 目录下，用于封装可复用的功能、类型、配置或 UI 组件。

## 1. 包的定义与结构

每个包都是一个独立的 Node.js 模块，拥有自己的 `package.json` 文件。

- **位置**: 包根据其功能和性质放置在 `packages/` 下的不同子目录中。主要的子目录包括：
  - `@core/`: 基础的 SDK 和 UI 组件库，不应包含任何业务逻辑。
  - `constants/`: 存放项目范围内的常量。
  - `effects/`: 包含具有副作用的逻辑，如 API 请求 (`request`)、布局 (`layouts`)、插件集成 (`plugins`) 等。
  - `icons/`: 图标资源库。
  - `locales/`: 国际化和多语言文件。
  - `preferences/`: 用户偏好设置相关的逻辑。
  - `stores/`: Pinia 状态管理模块。
  - `styles/`: 全局样式、主题或特定 UI 库的样式覆盖。
  - `types/`: TypeScript 类型定义。
  - `utils/`: 通用的工具函数库。
- **`package.json`**: 定义包的基本信息：
  - `name`: 包名，通常使用 `@scope/package-name` 的形式（如 `@amdox/request`）。
  - `version`: 包的版本号。
  - `type`: 推荐使用 `"module"`，以支持 ES Module。
  - `exports`: **（关键）** 定义包的入口点。明确指定其他包如何导入此包的内容。推荐指向 `src/index.ts` 或构建后的产物。
  - `dependencies` / `devDependencies`: 声明依赖。
  - `sideEffects`: 如果包包含 CSS 等副作用文件，需要声明。
- **`src` 目录**: 包含包的源代码（通常是 TypeScript）。
- **`tsconfig.json`**: （可选）如果需要特定的 TypeScript 配置，可以在包内定义，通常继承自根目录或 `internal/tsconfig` 的配置。

**示例 `package.json` 结构:**

```json
{
  "name": "@scope/my-package",
  "version": "1.0.0",
  "type": "module",
  "exports": {
    ".": {
      "types": "./src/index.ts", // 类型定义入口
      "default": "./src/index.ts" // 默认代码入口
    }
    // 可以定义其他子路径导出
    // "./feature": "./src/feature.ts"
  },
  "main": "./src/index.ts", // 兼容旧版 Node.js 或工具
  "module": "./src/index.ts", // 指向 ES Module 入口
  "types": "./src/index.ts", // 类型定义主入口
  "dependencies": {
    // ...
  },
  "devDependencies": {
    // ...
  },
  "sideEffects": false // 或 ["**/*.css"]
}
```

## 2. 依赖管理

Monorepo 利用 PNPM Workspaces 和 Catalogs 进行高效的依赖管理。

- **内部依赖 (Workspace Protocol)**:

  - 当一个包依赖于 Monorepo 内的另一个包时（例如 `@amdox/request` 依赖 `@amdox/utils`），使用 `workspace:*` 协议。
  - **示例**: `"@amdox/utils": "workspace:*"`
  - **优势**: PNPM 会自动链接到工作区内对应包的本地版本，无需发布和安装，便于开发和调试，确保使用的是最新的本地代码。

- **外部依赖 (Catalog Protocol)**:
  - 对于项目共享的外部依赖（如 `axios`, `vue`, `element-plus` 等），使用 `catalog:` 协议。
  - **示例**: `"axios": "catalog:"`
  - **优势**: 这些依赖的具体版本在根目录 `package.json` 的 `pnpm.catalog` 字段中统一定义。这确保了整个 Monorepo 项目中使用相同外部库的版本是一致的，避免了版本冲突和不一致性问题。

## 3. 入口与导出

- **`src/index.ts`**: 通常作为包的 主 入口文件。
- **`package.json` 的 `exports` 字段**: 是现代 Node.js 和构建工具识别包入口的标准方式，优先级高于 `main`, `module`。务必正确配置 `exports` 以确保包能被其他部分正确导入。
- **封装与再导出**: 包可以将内部实现细节封装起来，只通过 `index.ts` 导出公共 API。有时，为了方便下游包使用，也可以选择性地再导出其依赖项（如 `@amdox/request` 再导出了 `axios`）。

## 4. 构建与集成

- 包本身通常只包含源代码 (`.ts`, `.vue`, `.scss` 等)。
- 构建过程由根目录的 Vite 配置或 Turbo 统一管理。
- 应用（位于 `apps/` 目录下）或其他包可以直接通过包名导入（如 `import { useRequest } from '@amdox/request';`），构建工具会自动处理依赖关系。

遵循这些规范有助于保持 Monorepo 的结构清晰、依赖一致，并提高代码的可维护性和复用性。
