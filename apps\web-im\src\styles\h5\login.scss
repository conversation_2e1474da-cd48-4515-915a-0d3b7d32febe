@use '../common';

.login-h5 {
  min-width: 100%;
  max-width: 100%;
  padding: 17px 0;

  .login-header {
    padding: 0 23px;
  }

  .login-main {
    .login-main-content {
      align-items: flex-start;
      padding: 0 23px;
      background: url('../assets/image/h5/login-bg.png') no-repeat;
      background-position-x: right;
      background-size: 65%;

      .login-form {
        flex: 1;

        .login-title {
          flex-direction: column;
          padding: 60px 0 18px;

          p {
            padding-left: 0;
            font-size: 27px;
            line-height: 40px;
          }
        }

        .login-form-item {
          font-size: 18px;

          .el-checkbox {
            .checked-text {
              font-size: 14px;
            }
          }

          .login-form-item-disabled {
            padding: 20px;
            font-size: 18px;
          }
        }

        .login-btn {
          button {
            height: auto !important;
            padding: 13px 0;
            font-size: 20px;
            line-height: 27px;
          }
        }
      }
    }
  }

  .login-footer {
    padding: 10px;
    background: none;

    &-list {
      display: flex;
      flex: 1;

      &-item {
        display: flex;
        flex: 1;
        background: url('../assets/image/h5/adv-more.svg') no-repeat;
        background-size: 100% 100%;
        border: solid #96c3ff 1px;

        &:last-child {
          background: url('../assets/image/h5/adv-im.svg') no-repeat;
          background-size: 100% 100%;
        }

        a {
          box-sizing: border-box;
          display: flex;
          flex: 1;
          align-items: center;
          justify-content: space-around;
          padding: 20px;

          span {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 20px;
            font-family: PingFangSC-Regular;
            font-size: 15px;
            font-weight: 400;
            color: #fff;
            letter-spacing: 0;
            background: #147aff;
            border-radius: 30.5px;
            box-shadow:
              0 4px 5px 0 rgb(255 255 255 / 70%),
              0 3px 8px 0 rgb(20 122 255 / 55%);
          }

          aside {
            display: flex;
            flex-direction: column;

            h1 {
              font-family: PingFangSC-Regular;
              font-size: 16px;
              color: #000;
              letter-spacing: 0;
            }

            .sub {
              align-self: flex-end;
            }
          }
        }
      }

      &-bottom {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .text-header {
          display: flex;
          align-items: center;

          span {
            width: 84px;
            padding: 20px;
            font-family: 'PingFang SC';
            font-style: normal;
            font-weight: 400;
            color: #bbb;
          }
        }

        i {
          width: 120px;
          height: 1px;
          background: #dbdbdb;
        }

        &-image {
          display: flex;

          .platform {
            width: 41px;
            height: 41px;
            padding: 0 20px;

            img {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
  }
}
