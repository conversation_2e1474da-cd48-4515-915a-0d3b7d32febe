---
globs: packages/effects/chat-ui/src/**/*.vue
alwaysApply: false
---

# @amdox/chat-ui 国际化多语言适配规范

## 1. 导入服务

首先，在你的 Vue 组件的 `<script>` 部分导入 `$t`。

```typescript
import { $t } from '@amdox/locales';
```

## 2. 在模板中使用

你可以在 `<template>` 中直接使用 `$t(` 方法来翻译文本。

**示例:**
```vue
<template>
  // ...
  <button
    class="dialog-close-btn"
    :title="$t('im.xxxx.关闭')"
    @click="close"
  >
    <i class="icon icon-close" aria-hidden="true"></i>
  </button>
  // ...
  <button class="btn btn-cancel" @click="close">
    {{ $t('im.xxxx.取消') }}
  </button>
  <button class="btn btn-default" @click="submit">
    {{ $t('im.xxxx.确定') }}
  </button>
  // ...
</template>
```

## 3. 在脚本中使用

你也可以在 `<script>` 部分使用 `TUITranslateService`，例如为 props 设置默认值。

**示例:**
```typescript
// ...
const props = defineProps({
  // ...
  endPlaceholder: {
    default: () => $t('im.xxxx.开始时间'),
    type: String,
  },
  startPlaceholder: {
    default: () => $t('im.xxxx.开始时间'),
    type: String,
  },
  // ...
});
// ...
```

## 4. 管理和添加国际化文本

国际化文本资源位于 `packages/effects/chat-ui/src/locales` 目录下，并按语言代码进行组织。

### 4.1 文件结构

每个语言都对应一个目录，例如 `zh-CN`、`en-US`。在这些目录中，`*.ts` 文件包含了UI组件相关的翻译。

**按模块划分语言文件：**

为了更好地组织和管理国际化文本，建议按照模块来划分语言文件。每个模块可以拥有自己的语言文件，这样可以使文件结构更加清晰，易于维护。例如：

```
- packages/effects/chat-ui/src/locales/
  - zh-CN/
    - xxxx.ts             // xxxx模块文本
  - en-US/
    - xxxx.ts             // xxxx模块文本
```

- 阿拉伯语(沙特阿拉伯)文本: `packages/effects/chat-ui/src/locales/ar-SA/*.ts`
- 孟加拉语(孟加拉国)文本: `packages/effects/chat-ui/src/locales/bn-BD/*.ts`
- 英语(美国)文本: `packages/effects/chat-ui/src/locales/en-US/*.ts`
- 西班牙语(西班牙)文本: `packages/effects/chat-ui/src/locales/es-ES/*.ts`
- 法语(法国)文本: `packages/effects/chat-ui/src/locales/fr-FR/*.ts`
- 印地语(印度)文本: `packages/effects/chat-ui/src/locales/hi-IN/*.ts`
- 印尼语(印度尼西亚)文本: `packages/effects/chat-ui/src/locales/id-ID/*.ts`
- 日语(日本)文本: `packages/effects/chat-ui/src/locales/ja-JP/*.ts`
- 韩语(韩国)文本: `packages/effects/chat-ui/src/locales/ko-KR/*.ts`
- 马来语(马来西亚)文本: `packages/effects/chat-ui/src/locales/ms-MY/*.ts`
- 俄语(俄罗斯)文本: `packages/effects/chat-ui/src/locales/ru-RU/*.ts`
- 泰语(泰国)文本: `packages/effects/chat-ui/src/locales/th-TH/*.ts`
- 越南语(越南)文本: `packages/effects/chat-ui/src/locales/vi-VN/*.ts`
- 中文(简体)文本: `packages/effects/chat-ui/src/locales/zh-CN/*.ts`
- 中文(繁体)文本: `packages/effects/chat-ui/src/locales/zh-TW/*.ts`

每个文件都默认导出一个包含翻译键值对的对象。

### 4.2 添加新文本

当你需要添加一个新的翻译文本时，请遵循以下步骤：

1.  **使用中文作为 Key**: 所有翻译的 `Key` 都必须是中文原文。

2.  **更新中文文件**: 在 `packages/effects/chat-ui/src/locales/zh-CN/*.ts` 文件中，添加新的键值对，其中 `Key` 和 `Value` 都是中文原文。

    **示例:**
    ```typescript
    const xxxx = {
      // ...
      '这是一个新的文本': '这是一个新的文本',
    };
    ```

3.  **更新其他语言文件**: 在所有其他语言的 `*.ts` 文件（例如 `es-ES/*.ts`）中，添加相同的 `Key`，并将 `Value` 设置为对应的翻译。如果某个语言的翻译暂时不可用，可以暂时留空字符串作为 `Value`，但 `Key` 必须存在。

4.  **保持一致性**: 确保新的 `Key` 被添加到所有语言文件中，以避免在切换语言时出现显示问题。
