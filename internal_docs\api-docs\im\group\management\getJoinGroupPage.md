# 列表

## 接口信息

- **请求方法**：GET
- **接口地址**：/im/group/management/getJoinGroupPage

#### 请求参数

| 字段     | 类型   | 是否必传 | 描述       |
| -------- | ------ | -------- | ---------- |
| pageNum  | string | 是       | 页码       |
| pageSize | string | 是       | 每页的数量 |
| groupId  | string | 是       | 群id       |

#### 请求示例

```
GET https://test-web-im.amdox.com.cn/api/im/group/management/getJoinGroupPage?pageNum=1&pageSize=10&groupId=10000
```

#### 响应参数

| 字段 | 类型 | 是否必传 | 描述 |
| --- | --- | --- | --- |
| records | array | 是 | 数据列表 |
| records.id | string | 是 | 主键 |
| records.userIconPath | string | 是 | 用户头像存放路径 |
| records.applyId | string | 是 | 申请者im编号 |
| records.userType | number | 是 | 用户类型：1-教师 2学生 |
| records.joinGroupName | string | 是 | 名称 |
| records.joinGroupState | number | 是 | 1-待审核 2审核通过 3驳回申请 |
| records.remark | null | 否 | 备注 |
| total | number | 是 | 频道内的总人数。该字段仅在通信场景（mode 的值为 1 ）下返回。 |
| size | number | 否 | 非必传，不传默认为100（个页面显示的频道数量，取值范围为 [1,500]，默认值为 100。） |
| current | number | 是 | 当前页 |
| orders | array | 是 | 排序字段信息 |
| optimizeCountSql | boolean | 是 | - |
| searchCount | boolean | 是 | - |
| maxLimit | null | 否 | - |
| countId | null | 否 | - |
| pages | number | 是 | 当前分页总页数 |

#### 响应示例

```json
{
  "records": [
    {
      "id": "5",
      "userIconPath": "https://test.amdox.com.cn/static/school_v5/0/0/12/teachingAssistant/data/10011917/other/2025-03-19/801cb87d-a79e-48d5-8d36-4fdfd90fbe88.png",
      "applyId": "TEACHER_10011917",
      "userType": 1,
      "joinGroupName": "ywy",
      "joinGroupState": 0,
      "remark": null
    }
  ],
  "total": 2,
  "size": 10,
  "current": 1,
  "orders": [],
  "optimizeCountSql": true,
  "searchCount": true,
  "maxLimit": null,
  "countId": null,
  "pages": 1
}
```

#### 详细说明

studentId有值就是学生好友列表，或者就是当前登录的好友
