<script lang="ts" setup>
import { onMounted, onUnmounted, ref } from 'vue';

import { webviewAdapter } from '@amdox/webview';

import VueOfficePptx from '@vue-office/pptx';
import { ElMessage } from 'element-plus';

import { useHiddenLoading } from '#/hooks/useLoading';
import { useCreatePPTXPreview } from '#/hooks/usePPTX';
import { PPTEventBus } from '#/utils/event-bus';

// 定义组件名称
defineOptions({ name: 'PresentationPreview' });

// 定义 props
defineProps<{ src: string }>();

const emits = defineEmits<{
  (e: 'loading'): void;
}>();

const { handleHiddenLoading } = useHiddenLoading(emits);
const isShowPreview = ref(false);
const currentPageIndex = ref(0);
const totalPages = ref(0);

// 渲染完成事件处理
const renderedHandler = async () => {
  const pptx = document.querySelector('.vue-office-pptx') as HTMLElement;

  if (pptx) {
    const pptxPreviewWrapper = pptx.querySelector(
      '.pptx-preview-wrapper',
    ) as HTMLElement;
    if (pptxPreviewWrapper) {
      pptxPreviewWrapper.style.overflowY = 'hidden';
      pptxPreviewWrapper.style.overflowX = 'auto';
    }
    // 查询 pptx 元素内 class=pptx-preview-slide-wrapper 的全部元素
    const previewSlideWrappers = pptx.querySelectorAll(
      '.pptx-preview-slide-wrapper',
    ) as unknown as HTMLElement[];

    // 设置总页数
    totalPages.value = previewSlideWrappers.length;
    let currentLeft = 0;
    for (const previewSlideWrapper of previewSlideWrappers) {
      previewSlideWrapper.style.margin = '0';
      previewSlideWrapper.style.position = 'absolute';
      previewSlideWrapper.style.top = '50%';
      previewSlideWrapper.style.transform = 'translateY(-50%)';
      previewSlideWrapper.style.left = `${currentLeft}px`;
      currentLeft += previewSlideWrapper.offsetWidth;
    }
  }

  const jsonStr = JSON.stringify({
    pageIndex: currentPageIndex.value + 1,
    totalCount: totalPages.value,
  });
  webviewAdapter.getPageInfoRes?.(jsonStr);
  handleHiddenLoading();
  const previewList = await useCreatePPTXPreview(handleHiddenLoading);
  try {
    webviewAdapter.getPreviewRes?.(previewList.value);
  } catch (error: any) {
    ElMessage.error(error.message || 'getPreviewRes not found');
  }
};

// 错误事件处理
const errorHandler = (error: any) => {
  console.error('pptx error', error);
  ElMessage.error('PPT 加载失败');
};

// 滚动到指定页面
const scrollToPage = (pageIndex: number) => {
  const pptx = document.querySelector('.vue-office-pptx') as HTMLElement;
  if (pptx) {
    const pptxPreviewWrapper = pptx.querySelector(
      '.pptx-preview-wrapper',
    ) as HTMLElement;
    const previewSlideWrappers = pptx.querySelectorAll(
      '.pptx-preview-slide-wrapper',
    ) as unknown as HTMLElement[];

    if (pptxPreviewWrapper && previewSlideWrappers[pageIndex]) {
      const targetSlide = previewSlideWrappers[pageIndex];
      const targetLeft = Number.parseInt(targetSlide.style.left) || 0;
      pptxPreviewWrapper.scrollTo({
        left: targetLeft,
        behavior: 'smooth',
      });
    }
  }
};

// 上一页
const prevPage = () => {
  if (currentPageIndex.value > 0) {
    currentPageIndex.value--;
    scrollToPage(currentPageIndex.value);
  }
};

// 下一页
const nextPage = () => {
  if (currentPageIndex.value < totalPages.value - 1) {
    currentPageIndex.value++;
    scrollToPage(currentPageIndex.value);
  }
};

// 设置当前页面
const setCurrentPage = (pageIndex: number) => {
  if (pageIndex >= 0 && pageIndex < totalPages.value) {
    currentPageIndex.value = pageIndex;
    scrollToPage(currentPageIndex.value);
  }
};

// 响应获取页面信息请求
const handleGetPageInfo = () => {
  PPTEventBus.responsePageInfo(currentPageIndex.value + 1, totalPages.value);
};

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (
    event.key === 'ArrowLeft' ||
    event.key === 'ArrowUp' ||
    event.key === 'PageUp'
  ) {
    event.preventDefault();
    prevPage();
  } else if (
    event.key === 'ArrowRight' ||
    event.key === 'ArrowDown' ||
    event.key === 'PageDown' ||
    event.key === ' '
  ) {
    event.preventDefault();
    nextPage();
  }
};

function handleTouchmove(e: any) {
  e.preventDefault();
  e.stopPropagation();
}

onMounted(() => {
  // 获取窗口的显示宽度和显示高度
  const windowWidth = window.innerWidth;
  const windowHeight = window.innerHeight;

  // 检查 16:9 比例情况下，应该以宽度优先还是高度优先，才能完整显示PPT幻灯页
  const isWidthPriority = windowWidth / windowHeight > 16 / 9;

  if (isWidthPriority) {
    const pptx = document.querySelector('.preview-container') as HTMLElement;
    // 计算出适配16:9比例的宽度
    const width = (windowHeight * 16) / 9;
    pptx.style.width = `${width}px`;
  }

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown);
  document.addEventListener('mousemove', handleTouchmove, { passive: false });
  document.addEventListener('touchmove', handleTouchmove, { passive: false });

  // 监听事件总线的翻页事件
  PPTEventBus.onNextPage(nextPage);
  PPTEventBus.onPrevPage(prevPage);
  PPTEventBus.onGoToPage(({ pageIndex }) => setCurrentPage(pageIndex));
  PPTEventBus.onGetPageInfo(handleGetPageInfo);
  isShowPreview.value = true;
});

onUnmounted(() => {
  // 移除事件总线监听
  PPTEventBus.offNextPage(nextPage);
  PPTEventBus.offPrevPage(prevPage);
  PPTEventBus.offGoToPage();
  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeydown);
  PPTEventBus.offGetPageInfo(handleGetPageInfo);
  document.removeEventListener('mousemove', handleTouchmove);
  document.removeEventListener('touchmove', handleTouchmove);
});
</script>

<template>
  <VueOfficePptx
    v-if="isShowPreview"
    :src="src"
    @rendered="renderedHandler"
    @error="errorHandler"
  />
</template>

<style lang="scss">
body {
  overflow: hidden;
}

.preview-container {
  margin: 0 auto;
}

/* 针对 WebKit 浏览器（如Chrome, Safari）隐藏滚动条 */
.pptx-preview-wrapper::-webkit-scrollbar {
  display: none; /* 隐藏滚动条 */
}
</style>
