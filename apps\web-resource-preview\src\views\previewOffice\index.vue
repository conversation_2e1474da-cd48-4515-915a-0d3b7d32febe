<script lang="ts" setup>
// 导入 Vue 核心功能：生命周期钩子 (onMounted, onUnmounted)、响应式数据 (reactive, ref)、数据监听 (watch)
import { nextTick, onMounted, onUnmounted, reactive, ref, watch } from 'vue';
// 导入 Vue Router 的路由钩子，用于获取路由参数
import { useRoute } from 'vue-router';

// 导入 webview 适配器，用于与客户端 WebView 进行通信
import { webviewAdapter } from '@amdox/webview';

import { ElInput } from 'element-plus';

// 导入 PPT 事件总线，用于处理 PPT 相关的事件（如翻页、动画）
import { PPTEventBus } from '#/utils/event-bus';

// 加载样式
import Loading from '../../components/loading/Loading.vue';

/**
 * @description 预览信息接口定义
 * 定义了预览文件（如 PPT、文档）的相关信息结构。
 * @property fileName 文件名：当前预览文件的名称。
 * @property pageCount 页数：文件的总页数。
 * @property list 预览图片列表：包含每页预览图片 URL 的数组。
 */
interface PreViewInfo {
  fileName: string;
  pageCount: number;
  list: string[];
}

interface OfficePageInfo {
  anim?: { idx: string; total: string };
  ev?: string;
  hrefVal?: string;
  info?: string;
  load?: string;
  page?: { idx: string; total: string };
  preViewInfo: PreViewInfo;
  url?: string;
}
// 是否加载中
const isLoading = ref(false);
// 获取当前路由实例
const route = useRoute();
// 是否处于测试模式，控制页面上测试操作按钮的显示
const isTest = ref(true);
// iframe 容器的引用，用于操作样式或事件
const iframeContainer = ref<HTMLElement | null>(null);
// iframe 元素的引用，用于向 iframe 发送消息或获取其内容窗口
const ifreamRef = ref<HTMLIFrameElement | null>(null);

// iframe 的 src 地址
const ifreamSrc = ref('');
// 存储 iframe 参数的数组，用于构建 src 地址和 PPT 预览 API 地址
const ifreamParams = ref<string[]>([]);
// 预览服务的基础 URL
const baseUrl = ref('https://vip.ow365.cn/');

// 默认的 i 值，用于构建预览 URL
const I = 35_973;

// 响应式的 i 值，可从路由参数中获取或使用默认值
const i = ref<number>(I);
// 判断当前资源是否为 HTTPS
const isHttps = ref(true);
// 文件扩展名，如 'pptx', 'docx', 'pdf' 等
const fileExtension = ref('');
// 资源的 URL，从路由参数中获取
const resourceUrl = ref<string | undefined>(undefined);
// htool 参数，用于控制预览工具的行为
const htool = ref<string | undefined>(undefined);
// n 参数，通常表示页数或相关数量
const n = ref<string | undefined>(undefined);
// info 参数，用于传递额外信息给预览服务
const info = ref<string | undefined>(undefined);

// 响应式的预览信息对象，包含文件名、页数和图片列表
const previewInfo = reactive<PreViewInfo>({
  fileName: '',
  pageCount: 0,
  list: [],
});

const officePageInfo = ref<OfficePageInfo>({
  anim: { idx: '', total: '' },
  ev: '',
  hrefVal: '',
  info: '',
  load: '',
  page: { idx: '', total: '' },
  preViewInfo: previewInfo,
  url: '',
});

// 控制 iframe 是否可见的响应式变量
const isIframeVisible = ref(false);

// 在组件加载时，向 window 添加 'message' 事件监听器，用于接收 iframe 发送的消息
window.addEventListener('message', listenerMessage);

/**
 * @description 获取路由参数并初始化预览
 * 该函数在组件加载时和路由参数变化时被调用，用于从路由中提取预览所需的文件信息，
 * 并根据文件类型生成 iframe 的 src 地址或触发 PPT 预览图片的获取。
 * @return {void}
 */
async function getRouteQueryParams() {
  isLoading.value = true;
  // 从路由查询参数中解构出文件 URL (furl) 和其他相关参数 (htool, info, n, i)
  const {
    furl,
    htool: _htool,
    info: _info,
    n: _n,
    i: _i,
    isTest: _isTest,
  } = route.query;
  isTest.value = !!_isTest;
  // 重置预览信息的状态，确保每次加载新文件时数据是干净的
  previewInfo.fileName = '';
  previewInfo.pageCount = 0;
  previewInfo.list = [];
  officePageInfo.value = {
    anim: { idx: '', total: '' },
    ev: '',
    hrefVal: '',
    info: '',
    load: '',
    page: { idx: '', total: '' },
    preViewInfo: previewInfo,
    url: '',
  };
  // 如果没有文件 URL，则直接返回，不进行后续操作
  if (!furl) {
    return;
  }

  // 更新响应式变量 resourceUrl 为当前文件的 URL
  resourceUrl.value = furl as string;
  // 初始时隐藏 iframe，待 src 构建完成后再显示
  isIframeVisible.value = false;
  // 从文件 URL 中提取文件扩展名
  fileExtension.value =
    (furl as string).split('.').pop()?.toLocaleLowerCase() || '';
  // 判断文件 URL 是否为 HTTPS 协议
  isHttps.value = (furl as string).includes('https:');
  // 更新 htool, info, n 参数，这些参数可能影响预览服务的行为
  htool.value = _htool as string;
  info.value = _info as string;
  n.value = _n as string;
  // 更新 i 参数，如果路由中没有提供，则使用默认值 I
  i.value = (Number(_i) as number) || I;

  // 调用 crateIfreamScr 函数，异步创建 iframe 的 src 地址
  const _ifreamSrc = await crateIfreamScr(furl as string, fileExtension.value);

  // 如果文件是 PPT 或 PPTX 格式，则触发获取 PPT 预览图片的操作
  if (['docx', 'dox', 'pdf', 'ppt', 'pptx'].includes(fileExtension.value)) {
    await getPPTPreview();
  }

  // 更新 iframe 的 src 属性
  ifreamSrc.value = _ifreamSrc;
  // 设置 iframe 可见，使其在页面上显示
  isIframeVisible.value = true;
  // 打印日志，表示 iframe 已重新可见，并显示新的 src 地址
  const fileExtensions = ['ppt', 'pptx', 'pdf'];
  // 如果文件扩展名是 ppt, pptx, pdf 之一，则直接返回，不处理非字符串数据
  if (!fileExtensions.includes(fileExtension.value)) {
    nextTick(addIFreamEventListener);
  }
}

/**
 * @description: ifream  添加加载完毕监听
 * @return {*}
 */
function addIFreamEventListener() {
  ifreamRef.value?.addEventListener('load', ifreamOnload);
}

/**
 * @description:ifream加载完毕监听事件
 * @return {*}
 */
function ifreamOnload() {
  isLoading.value = false;
  // eslint-disable-next-line no-console
  console.log('<<< 【 WEB LOG】 previewInfo >>>', previewInfo);
  officePageInfo.value.preViewInfo = { ...previewInfo };

  webviewAdapter.getOfficePageInfoRes?.(officePageInfo.value);
}

// 页面加载时立即获取路由参数，进行首次预览初始化
getRouteQueryParams();

// 监听路由 query 参数变化
// 当路由的查询参数发生变化时，重新调用 getRouteQueryParams 函数，以更新预览内容。
watch(
  () => route.query,
  () => {
    // 当路由参数发生变化时，重新获取参数并初始化预览
    getRouteQueryParams();
  },
  { deep: true }, // 深度监听，确保对象内部属性的变化也能触发监听器
);

/**
 * @description 生成 iframe src 地址
 * 根据文件 URL 和文件扩展名，以及其他相关参数，动态构建用于 iframe 的完整 src 地址。
 * 这个地址将指向在线预览服务的接口。
 * @param {string} fileUrl 文件的网络 URL，例如 OSS 上的文件地址。
 * @param {string} fileExtension 文件后缀名，例如 'pptx', 'docx'。
 * @return {Promise<string>} 返回一个 Promise，解析为拼接后的 iframe src 字符串。
 */
function crateIfreamScr(
  fileUrl: string,
  fileExtension: string,
): Promise<string> {
  return new Promise((resolve) => {
    const urls = []; // 用于存储所有 URL 参数的数组
    // 初始化 URL，包含基础地址和默认的 i 参数
    let _url = `${baseUrl.value}?i=${i.value}`;
    urls.push(_url); // 将初始 URL 添加到参数数组

    // 根据文件扩展名处理 PPT/PPTX 文件的特定参数
    if (['ppt', 'pptx'].includes(fileExtension)) {
      // 如果 htool 参数已存在，则使用其值；否则，默认设置为 '1'
      if (htool.value) {
        _url = `${_url}&htool=${htool.value}`;
        urls.push(`htool=${htool.value}`);
      } else {
        _url = `${_url}&htool=1`;
        urls.push(`htool=1`);
      }
      // 如果 n 参数已存在，则使用其值；否则，默认设置为 '5'（可能表示页数或相关配置）
      if (n.value) {
        _url = `${_url}&n=${n.value}`;
        urls.push(`n=${n.value}`);
      } else {
        _url = `${_url}&n=5`;
        urls.push('n=5');
      }
    }

    // 根据文件扩展名处理 DOC/DOCX 文件的特定参数
    if (['doc', 'docx'].includes(fileExtension)) {
      // 如果 n 参数已存在，则使用其值；否则，默认设置为 '1'
      if (n.value) {
        _url = `${_url}&n=${n.value}`;
        urls.push(`n=${n.value}`);
      } else {
        _url = `${_url}&n=1`;
        urls.push('n=1');
      }
    }
    // 如果是 HTTPS 资源，则添加 ssl=1 参数
    if (isHttps.value) {
      _url = `${_url}&ssl=${1}`;
      urls.push('ssl=1');
    }

    // 添加文件 URL 参数
    urls.push(`furl=${fileUrl}`);
    // 将构建好的参数数组赋值给 ifreamParams，用于后续 PPT 预览 API 的调用
    ifreamParams.value = [...urls];
    // 将所有参数拼接成最终的 iframe src 字符串
    const ifreamSrc = urls.join('&') as string;
    // 解析 Promise，返回构建好的 src 字符串
    return resolve(ifreamSrc);
  });
}

/**
 * @description 获取 PPT 预览图片（流式接口）
 * 该函数通过向预览服务发送请求，以流式方式获取 PPT 的预览图片信息。
 * 主要用于获取 PPT 的文件名、总页数和每页的缩略图列表。
 * @return {Promise<boolean>} 返回一个 Promise，成功时解析为 true，失败时解析为 Error。
 */
function getPPTPreview() {
  return new Promise((resolve, reject) => {
    // 复制 iframe 参数，用于构建 PPT 预览 API 的 URL
    const urls = [...ifreamParams.value];
    const len = urls.length;

    // 检查是否已存在 'info=' 参数，如果存在则更新其值为 '6'，否则在 furl 参数前插入 'info=6'
    if (urls.includes('info=')) {
      const index = urls.findIndex((item) => item.includes('info='));
      if (index !== -1) {
        urls[index] = `info=${6}`;
      }
    } else {
      // 在倒数第二个位置（即 furl 参数之前）插入 'info=6'
      urls.splice(len - 1, 0, `info=6`);
    }

    // 拼接所有参数，形成最终的 API 请求 URL
    const apiUrl = urls.join('&');
    // 打印 API 请求 URL，用于调试
    // 发送 fetch 请求到构建好的 API URL
    window
      .fetch(apiUrl)
      .then(async (res) => {
        // 如果响应为空，则拒绝 Promise 并抛出错误
        if (!res) {
          return reject(new Error(`getPPTPreview:${res}`));
        }
        // 打印 API 响应，用于调试

        // 获取响应体的 ReadableStream reader
        const reader = res?.body?.getReader();
        // 创建 TextDecoder 实例，用于解码流数据
        const decoder = new TextDecoder();
        // 如果没有 reader，则直接返回
        if (!reader) return;

        // 循环读取流数据，直到流结束
        while (true) {
          // 读取流的下一部分数据，value 是数据块，done 表示流是否已结束
          const { value, done } = await reader.read();
          // 打印读取到的值和状态，用于调试
          // 如果流已结束，则跳出循环
          if (done) break;

          // 将读取到的 Uint8Array 数据解码为字符串
          const valueStr = decoder.decode(value);
          // 将解码后的字符串解析为 JSON 对象
          const data = JSON.parse(valueStr) as any;

          // 更新预览信息的状态
          previewInfo.fileName = data.FileName;
          previewInfo.pageCount = data.PageCount;
          previewInfo.list = data.Thumbnails;
        }

        // 流数据处理完毕，解析 Promise 为 true
        resolve(true);
      })
      .catch((error) => {
        // 捕获 fetch 请求或流处理过程中的错误
        console.error('Error fetching PPT preview:', error);
        reject(error);
      });
  });
}

const filePage = ref<any>();
/**
 * @description 监听 iframe 发送的消息，转发给 webview 适配器
 * 该函数处理来自 iframe 的消息，通常包含预览文件的页面信息。它会解析消息数据，
 * 并通过 webviewAdapter 将处理后的信息传递给客户端 WebView。
 * @param {MessageEvent} event message 事件对象，包含来自 iframe 的数据。
 */
async function listenerMessage(event: MessageEvent) {
  isLoading.value = false;
  const { data } = event; // 从事件对象中解构出数据
  // 打印接收到的数据，用于调试

  // 检查数据是否有效（非空且为字符串类型）
  if (!data || typeof data !== 'string') {
    const fileExtensions = ['ppt', 'pptx', 'pdf'];
    // 如果文件扩展名是 ppt, pptx, pdf 之一，则直接返回，不处理非字符串数据
    if (fileExtensions.includes(fileExtension.value)) {
      return;
    } else {
      // 对于其他文件类型，如果数据无效，则向 webviewAdapter 发送 null
      // eslint-disable-next-line no-console
      console.log(
        '<<< 【 WEB LOG】 officePageInfo.value >>>',
        officePageInfo.value,
      );
      webviewAdapter.getOfficePageInfoRes?.(officePageInfo.value);
    }
    return;
  }

  let pageInfo: any; // 定义 pageInfo 变量，用于存储解析后的数据

  try {
    // 尝试将接收到的数据解析为 JSON 对象
    pageInfo = JSON.parse(data);
    if (pageInfo && pageInfo.page) {
      filePage.value = pageInfo?.page;
    }
    // 将当前预览信息 (previewInfo) 合并到 pageInfo 中
    // pageInfo.previewInfo = { ...previewInfo };

    // 如果 pageInfo 成功解析且有效
    if (pageInfo) {
      // 打印解析后的 pageInfo，用于调试
      try {
        // 通过 webviewAdapter 将 pageInfo 传递给客户端 WebView
        // eslint-disable-next-line no-console
        console.log(
          '<<< 【 WEB LOG】 officePageInfo.value >>>',
          officePageInfo.value,
          pageInfo,
        );
        officePageInfo.value = Object.assign(officePageInfo.value, pageInfo);
        officePageInfo.value.preViewInfo = { ...previewInfo };
        webviewAdapter.getOfficePageInfoRes?.(officePageInfo.value);
        // 打印调用成功日志
        // eslint-disable-next-line no-console
        console.log('[DEBUG] webviewAdapter.getOfficePageInfoRes 调用成功');
      } catch (error) {
        // 捕获并打印 webviewAdapter 调用过程中的错误
        console.error(
          '[DEBUG] webviewAdapter.getOfficePageInfoRes 调用出错:',
          error,
        );
      }
    } else {
      // 如果 pageInfo 无效，打印调试信息
      // eslint-disable-next-line no-console
      console.log('[DEBUG] pageInfo 无效，不调用 webviewAdapter');
    }
  } catch (error) {
    // 捕获 JSON 解析失败的错误，并打印错误信息和原始数据
    console.error('[DEBUG] JSON 解析失败:', error);
    // // eslint-disable-next-line no-console
    // console.log('[DEBUG] 原始 data 内容:', data);
  }
  // 打印 listenerMessage 处理完成的日志

  // console.log('[DEBUG] listenerMessage 处理完成');
}

/**
 * @description PPT 上一页
 * 向 iframe 发送 'prePage' 消息，通知预览服务切换到上一页。
 */
function prevPage() {
  // 确保 iframe 引用存在且内容窗口可用
  if (!ifreamRef.value) return;
  ifreamRef.value.contentWindow?.postMessage('prePage', '*');
}

/**
 * @description PPT 下一页
 * 向 iframe 发送 'nextPage' 消息，通知预览服务切换到下一页。
 */
function nextPage() {
  // 确保 iframe 引用存在且内容窗口可用
  if (!ifreamRef.value) return;
  ifreamRef.value.contentWindow?.postMessage('nextPage', '*');
}

/**
 * @description PPT 上一帧动画
 * 向 iframe 发送 'preAnim' 消息，通知预览服务播放上一帧动画。
 */
function preAnim() {
  // 确保 iframe 引用存在且内容窗口可用
  if (!ifreamRef.value) return;
  ifreamRef.value.contentWindow?.postMessage('preAnim', '*');
}

/**
 * @description PPT 下一帧动画
 * 向 iframe 发送 'nextAnim' 消息，通知预览服务播放下一帧动画。
 */
function nextAnim() {
  // 确保 iframe 引用存在且内容窗口可用
  if (!ifreamRef.value) return;
  ifreamRef.value.contentWindow?.postMessage('nextAnim', '*');
}

/**
 * @description 跳转到指定页码
 * 向 iframe 发送 'goPage,${page}' 消息，通知预览服务跳转到指定页码。
 * @param {number} page 目标页码。
 */
function jumpToPage(page: number) {
  // 确保 iframe 引用存在且内容窗口可用
  if (!ifreamRef.value) return;
  ifreamRef.value.contentWindow?.postMessage(`goPage,${page}`, '*');
}

// /**
//  * @description: 禁止操作
//  * @return {*}
//  */
// function noOperation() {
//   if (iframeContainer.value) {
//     iframeContainer.value.style.pointerEvents = 'none';
//   }
// }
// /**
//  * @description: 恢复操作
//  * @return {*}
//  */
// function resetOperation() {
//   if (iframeContainer.value) {
//     iframeContainer.value.style.pointerEvents = 'auto';
//   }
// }

/**
 * @description 重置预览数据
 * 将预览信息 (fileName, pageCount, list) 和 iframe 可见状态重置为初始值。
 */
function resetData() {
  // 重置预览信息的状态
  previewInfo.fileName = '';
  previewInfo.pageCount = 0;
  previewInfo.list = [];
  // 隐藏 iframe
  isIframeVisible.value = false;
}

/**
 * @description 强制刷新预览
 * 该函数提供给客户端 WebView 调用，用于强制重新加载和刷新预览内容。
 * 它会重置当前预览数据，从而触发组件重新获取和渲染预览。
 */
function forceReloadPreview() {
  // 打印日志，表示强制刷新被触发
  // 调用 resetData 函数重置所有预览相关的数据
  resetData();
}
// 将 forceReloadPreview 方法挂载到 window 对象上，使其可以被外部（如 WebView）调用
(window as any).forceReloadPreview = forceReloadPreview;

// 组件挂载生命周期钩子
onMounted(() => {
  // 打印加载完成日志

  // 监听事件总线上的 PPT 翻页和动画事件，并绑定到相应的处理函数
  PPTEventBus.onNextPage(nextPage);
  PPTEventBus.onPrevPage(prevPage);
  PPTEventBus.onNextAnim(nextAnim);
  PPTEventBus.onPrevAnim(preAnim);
  PPTEventBus.onJumpToPage(jumpToPage);
  // 监听事件总线上的强制刷新预览事件
  PPTEventBus.onForceReloadPreview(forceReloadPreview);
});

// 组件卸载生命周期钩子
onUnmounted(() => {
  // 移除 window 上的 'message' 事件监听器，防止内存泄漏
  window.removeEventListener('message', listenerMessage);
  // 移除事件总线上的所有监听器
  PPTEventBus.offNextPage(nextPage);
  PPTEventBus.offPrevPage(prevPage);
  PPTEventBus.offNextAnim(nextAnim);
  PPTEventBus.offPrevAnim(preAnim);
  PPTEventBus.offJumpToPage(jumpToPage);
  PPTEventBus.offForceReloadPreview(forceReloadPreview);
  // 从 window 对象上移除挂载的 forceReloadPreview 方法
  if ((window as any).forceReloadPreview) {
    delete (window as any).forceReloadPreview;
  }
  ifreamRef.value?.removeEventListener('load', ifreamOnload);
});

const pageIndex = ref(undefined);
function gotoIndex() {
  // eslint-disable-next-line no-console
  console.log(pageIndex.value);
  if (pageIndex.value !== undefined) {
    jumpToPage(pageIndex.value > 0 ? pageIndex.value - 1 : pageIndex.value);
    pageIndex.value = undefined;
  }
}

function handlerKeydown(event: any) {
  if (event.key === 'Enter') {
    gotoIndex();
  }
}
</script>

<!-- 模板部分 -->
<template>
  <!-- iframe 容器，用于包裹 iframe 和操作按钮 -->
  <div class="iframe-container" ref="iframeContainer">
    <!-- 测试操作按钮区域，仅在 isTest 为 true 时显示 -->
    <div v-if="isTest" class="iframe-container-operation">
      <!-- 上一页按钮 -->
      <button class="iframe-container-operation-btn" @click="prevPage">
        上一页
      </button>
      <!-- 下一页按钮 -->
      <button class="iframe-container-operation-btn" @click="nextPage">
        下一页
      </button>
      <!-- 上一帧动画按钮 -->
      <button class="iframe-container-operation-btn" @click="preAnim">
        上一帧动画
      </button>
      <!-- 下一帧动画按钮 -->
      <button class="iframe-container-operation-btn" @click="nextAnim">
        下一帧动画
      </button>
      <div class="iframe-container-operation-page">
        <ElInput
          v-model="pageIndex"
          placeholder="请输入页码"
          style="width: 178px"
          @keydown="handlerKeydown"
        />
        <span> 页 </span>
        <button @click="gotoIndex">跳转</button>
        <span>{{ filePage?.idx }}/{{ filePage?.total }}</span>
      </div>
    </div>
    <!-- 预览 iframe，仅在 isIframeVisible 为 true 时显示 -->
    <iframe
      v-if="isIframeVisible"
      class="iframe-container-content"
      ref="ifreamRef"
      :src="ifreamSrc"
    ></iframe>
    <div v-show="isLoading" class="amdox-loading-container">
      <Loading
        :is-show-label="false"
        :radius="22"
        :label-style="() => ({ fontSize: '20px' })"
      />
    </div>
  </div>
</template>

<!-- 全局样式部分 -->
<style lang="scss">
body {
  padding: 0;
  margin: 0;
}
</style>

<!-- 局部作用域样式部分 -->
<style lang="scss" scoped>
.iframe-container {
  width: 100vw; // 宽度占满整个视口宽度
  height: 100vh; // 高度占满整个视口高度
  overflow: hidden; // 隐藏溢出内容
  background: #fff;

  &-operation {
    position: absolute; // 绝对定位
    top: 0; // 距离顶部 0
    left: 50%; // 距离左侧 50%
    z-index: 999; // 层级最高，确保在最上层
    transform: translateX(-50%); // 水平居中

    &-btn {
      // 按钮之间的间距
      & + & {
        margin-left: 8px;
      }
    }

    &-page {
      display: flex;
      align-items: center;
      margin-top: 8px;
    }
  }

  &-content {
    width: 100%; // 宽度占满父容器
    height: 100%; // 高度占满父容器
    border: none; // 无边框
  }

  .amdox-loading-container {
    position: fixed;
    inset: 0;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: rgb(255 255 255);
  }
}
</style>
