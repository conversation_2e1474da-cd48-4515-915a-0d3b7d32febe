<script lang="ts" setup>
// 引入VueOfficeExcel组件
import VueOfficeExcel from '@vue-office/excel';

import { useHiddenLoading } from '#/hooks/useLoading';

// 引入相关样式
import '@vue-office/excel/lib/index.css';

// 定义组件名称
defineOptions({ name: 'SpreadsheetsPreview' });

// 定义 props
defineProps<{ src: string }>();

const emits = defineEmits<{
  (e: 'loading'): void;
}>();

const { handleHiddenLoading } = useHiddenLoading(emits);
const renderedHandler = () => {
  // eslint-disable-next-line no-console
  console.log('excel rendered');
  handleHiddenLoading();
};

const errorHandler = (error: any) => {
  // eslint-disable-next-line no-console
  console.log('excel error', error);
};
</script>

<template>
  <VueOfficeExcel
    :src="src"
    style="height: 100vh"
    @rendered="renderedHandler"
    @error="errorHandler"
  />
</template>
