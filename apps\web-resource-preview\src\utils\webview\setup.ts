import type { WebviewProvide } from '@amdox/webview';

import { isWebviewEnv, setupWebviewProvideMethods } from '@amdox/webview';

import createWebviewProvide from './provides';

// 初始化 webview 环境
export const setupWebviewEnv = async () => {
  // Windows 环境
  // if (isWindowsEnv()) {
  //   const { newToken, oldToken } = await getTokens();
  //   // eslint-disable-next-line no-console
  //   console.log('<<< newToken >>>', newToken);
  //   // eslint-disable-next-line no-console
  //   console.log('<<< oldToken >>>', oldToken);
  // }
  // // QT 环境（<PERSON>ylin <PERSON>）
  // else if (isQtEnv()) {
  //   const { newToken, oldToken } = await getTokens();
  //   // eslint-disable-next-line no-console
  //   console.log('<<< newToken >>>', newToken);
  //   // eslint-disable-next-line no-console
  //   console.log('<<< oldToken >>>', oldToken);
  // }
  // // Android 环境
  // else if (isAndroidEnv()) {
  //   setZoom();

  //   const { newToken, oldToken } = await getTokens();
  //   // eslint-disable-next-line no-console
  //   console.log('<<< newToken >>>', newToken);
  //   // eslint-disable-next-line no-console
  //   console.log('<<< oldToken >>>', oldToken);
  // }

  const IS_DEBUG = false;

  if (isWebviewEnv() || IS_DEBUG) {
    // webview 调试模式
    const debugConfig = {
      enabled: IS_DEBUG,
      logEnabled: IS_DEBUG,
    };

    const webviewProvide = createWebviewProvide({
      debugPlatforms: [IS_DEBUG ? 'Android' : ''],
    }) as WebviewProvide;

    // 设置 Webview 主动调用的方法
    setupWebviewProvideMethods(webviewProvide, debugConfig);
  }
};
