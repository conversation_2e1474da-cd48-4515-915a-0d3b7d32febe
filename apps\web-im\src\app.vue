<script lang="ts" setup>
import { onMounted } from 'vue';
import { RouterView, useRouter } from 'vue-router';

import { useElementPlusDesignTokens } from '@amdox/hooks';

import { ElConfigProvider } from 'element-plus';

import { elementLocale } from '#/locales';
import { useAuthStore } from '#/store/auth';
import { setupWebviewEnv } from '#/utils/webview/setup';

defineOptions({ name: 'App' });

const router = useRouter();
const authStore = useAuthStore();

useElementPlusDesignTokens();

// 设置用户踢出监听
authStore.setupUserKickoutWatcher(router);

onMounted(async () => {
  await setupWebviewEnv();

  // 执行自动登录
  await authStore.autoIMLogin();
});
</script>

<template>
  <ElConfigProvider :locale="elementLocale">
    <RouterView />
  </ElConfigProvider>
</template>
