import type { Im } from '@amdox/types';

import { requestClient } from '#/api/request';

/**
 * 获取好友列表
 */
export async function getFriendListApi(data: Im.GetFriendListParams) {
  return requestClient.post<Im.GetFriendListResult>(
    '/im/address/book/getFriendList',
    data,
  );
}

/**
 * 添加好友申请
 */
export async function addFriendApplicationRelateApi(
  data: Im.AddFriendApplicationParams,
) {
  return requestClient.post<null>(
    '/im/address/book/addFriendApplicationRelate',
    data,
  );
}

/**
 * 分页查询好友申请记录
 */
export async function getFriendApplicationPageApi(
  params: Im.GetFriendApplicationPageParams,
) {
  return requestClient.get<Im.GetFriendApplicationPageResult>(
    '/im/address/book/page',
    { params },
  );
}

/**
 * 更新好友申请状态（接受或忽略）
 */
export async function updateFriendApplicationRelateApi(
  data: Im.UpdateFriendApplicationParams,
) {
  return requestClient.post<null>(
    '/im/address/book/updateFriendApplicationRelate',
    data,
  );
}

/**
 * 删除好友申请记录
 */
export async function deleteFriendApplicationRelateApi(
  data: Im.DeleteFriendApplicationParams,
) {
  return requestClient.post<null>(
    '/im/address/book/delFriendApplicationRelate',
    data,
  );
}

/**
 * 搜索用户添加
 */
export async function getImUserListBySearchApi(params: Im.SearchUserParams) {
  return requestClient.get<Im.SearchUserResult>(
    '/im/address/book/getImUserListBySearch',
    { params },
  );
}
