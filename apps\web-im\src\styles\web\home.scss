.home {
  box-sizing: border-box;
  display: flex;
  flex: 1;
  flex-direction: row;
  width: 100%;
  min-width: 970px;
  height: 100%;
  overflow: hidden;
  background-size: contain;

  .home-menu {
    box-sizing: border-box;
    display: flex;
  }

  .home-container {
    box-sizing: border-box;
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: stretch;
    overflow: hidden;

    .home-header {
      box-sizing: border-box;
      overflow: hidden;

      .header-menu-show {
        padding-left: 0;
      }
    }

    .home-main {
      box-sizing: border-box;
      display: flex;
      flex: 1;
      justify-content: center;
      overflow: hidden;

      .home-TUIKit {
        box-sizing: border-box;
        display: flex;
        flex: 1;
        width: 100%;
        height: 100%;
        overflow: hidden;

        .home-TUIKit-navbar {
          box-sizing: border-box;
          display: flex;
        }

        .home-TUIKit-main {
          box-sizing: border-box;
          display: flex;
          flex: 1;
          flex-direction: row;
          overflow: hidden;
          border: 0 solid black;

          .home-conversation,
          .home-relation,
          .home-contact-list {
            box-sizing: border-box;
            display: flex;
            flex: 0 0 24%;
            flex-direction: column;
            min-width: 285px;
            border-right: 1px solid #f4f5f9;

            .home-conversation-header,
            .home-contact-list-header {
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 0 12px 0 16px;

              &-title {
                display: flex;
                gap: 8px;
                align-items: center;
                font-size: 16px;
                font-weight: bold;
              }

              &-right {
                display: flex;

                .conversation-header-icon {
                  width: 24px;
                  height: 24px;
                  padding: 5px;
                  margin: 0 6px 0 0;
                  cursor: pointer;
                  border-radius: 6px;
                  transition: all 0.3s;

                  &:hover {
                    background: #f5f6f8;
                  }
                }
              }
            }
          }

          .home-chat {
            position: relative;
            box-sizing: border-box;
            display: flex;
            flex: 1;
            flex-direction: column;
            min-width: 0;
            overflow: hidden;
          }

          .home-contact-detail {
            position: relative;
            box-sizing: border-box;
            display: flex;
            flex: 1;
            flex-direction: column;
            min-width: 0;
            overflow: hidden;
          }
        }

        .callkit-drag-container {
          position: fixed;
          z-index: 100;
          user-select: none;
          background-color: #fff;

          &-pc {
            top: calc(50% - 18rem);
            left: calc(50% - 25rem);
            width: 50rem;
            height: 36rem;
            border-radius: 16px;
            box-shadow:
              rgb(0 0 0 / 16%) 0 3px 6px,
              rgb(0 0 0 / 23%) 0 3px 6px;
          }

          &-mini {
            top: 70px;
            right: 10px;
            width: 168px;
            height: 56px;
            background-color: transparent;
            border-radius: 0;
            box-shadow: none;
          }
        }
      }
    }
  }

  .home-container.menu-expand {
    .home-main .home-TUIKit .callkit-drag-container.callkit-drag-container-pc {
      left: calc(50% - 25rem + 150px);
    }
  }
}
