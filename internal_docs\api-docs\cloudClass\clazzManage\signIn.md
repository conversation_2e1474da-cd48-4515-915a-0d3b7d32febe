# 签到/未打卡

## 接口信息

- **请求方法**：POST
- **接口地址**：/cloudClass/clazzManage/signIn

#### Query 参数

| 字段         | 类型    | 是否必传 | 描述                        |
| ------------ | ------- | -------- | --------------------------- |
| checkUserId  | Integer | Yes      | 签到的学生id                |
| checkUuid    | String  | Yes      | 事件考勤uuid                |
| signInStatus | String  | Yes      | 打卡类型(0-未打卡，1已打卡) |

#### 请求示例

`POST /cloudClass/clazzManage/signIn?checkUserId=10010833&checkUuid=eaf461a987204b9cb8a5c5e045218ed9&signInStatus=1`

#### 响应参数

无数据返回

#### 响应示例

```json
{
  "data": null
}
```
