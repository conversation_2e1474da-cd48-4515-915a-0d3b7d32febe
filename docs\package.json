{"name": "@amdox/docs", "version": "5.5.5", "private": true, "scripts": {"build": "vitepress build", "dev": "vitepress dev", "docs:preview": "vitepress preview"}, "imports": {"#/*": {"node": "./src/_env/node/*", "default": "./src/_env/*"}}, "dependencies": {"@amdox-core/shadcn-ui": "workspace:*", "@amdox/common-ui": "workspace:*", "@amdox/locales": "workspace:*", "@amdox/plugins": "workspace:*", "@amdox/styles": "workspace:*", "ant-design-vue": "catalog:", "lucide-vue-next": "catalog:", "medium-zoom": "catalog:", "radix-vue": "catalog:", "vitepress-plugin-group-icons": "catalog:"}, "devDependencies": {"@amdox/vite-config": "workspace:*", "@nolebase/vitepress-plugin-git-changelog": "catalog:", "@vite-pwa/vitepress": "catalog:", "vitepress": "catalog:", "vue": "catalog:"}}