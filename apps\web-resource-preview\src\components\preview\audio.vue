<script lang="ts" setup>
import { onMounted, onUnmounted } from 'vue';

import { useHiddenLoading } from '#/hooks/useLoading';
// 定义 props
defineProps<{ src: string }>();
const emits = defineEmits<{
  (e: 'loading'): void;
}>();

const { bindEventListener, removeEventListener } = useHiddenLoading(emits);

onMounted(() => {
  bindEventListener('audio', 'loadedmetadata');
});

onUnmounted(() => {
  removeEventListener('audio', 'loadedmetadata');
});
</script>

<template>
  <div class="audio-preview">
    <audio :src="src" controls></audio>
  </div>
</template>

<style scoped>
.audio-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}
</style>
