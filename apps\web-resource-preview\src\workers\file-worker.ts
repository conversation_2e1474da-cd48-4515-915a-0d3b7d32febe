// 文件处理Worker

// 导入压缩库
import compress from 'browser-image-compression';

// 处理文件的主函数
async function processFile(file: File): Promise<ArrayBuffer> {
  try {
    // 如果是图片，进行压缩处理
    if (file.type.startsWith('image/')) {
      const options = {
        maxSizeMB: 1, // 最大文件大小
        maxWidthOrHeight: 1920, // 最大宽度或高度
        useWebWorker: true, // 使用Web Worker进行压缩
      };
      const compressedFile = await compress(file, options);
      return await compressedFile.arrayBuffer();
    }
    // 非图片文件直接返回ArrayBuffer
    return await file.arrayBuffer();
  } catch (error) {
    console.error('文件处理失败:', error);
    throw error;
  }
}

// 处理文件分片
async function processChunk(chunk: Blob): Promise<ArrayBuffer> {
  try {
    return await chunk.arrayBuffer();
  } catch (error) {
    console.error('分片处理失败:', error);
    throw error;
  }
}

// 监听主线程消息
globalThis.addEventListener('message', async (event) => {
  try {
    const { type, file, chunk } = event.data;
    let result: ArrayBuffer;
    if (type === 'processFile') {
      result = await processFile(file);
    } else if (type === 'processChunk') {
      result = await processChunk(chunk);
    } else {
      throw new Error('未知的处理类型');
    }

    globalThis.postMessage(result, { transfer: [result] });
  } catch (error) {
    globalThis.postMessage({ error: (error as Error).message });
  }
});
