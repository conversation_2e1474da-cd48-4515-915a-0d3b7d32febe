import type { Im } from '@amdox/types';

import { requestClient } from '#/api/request';

export namespace ImGroupManagementApi {
  /** 获取行政班成员列表接口参数 */
  export interface GetAdministrativeClassMemberListParams {
    clazzId: string;
  }

  /** 获取行政班成员列表接口返回值 */
  export interface GetAdministrativeClassMemberListResult {
    studentList: Im.UserInfo[];
    teacherList: Im.UserInfo[];
    patriarchList: Im.PatriarchInfo[];
  }

  /** 获取行政班列表接口返回值 */
  export type GetAdministrativeClassListResult = Im.ClassInfo[];

  /** 获取行政班列表接口参数 */
  export interface GetAdministrativeClassListParams {
    campusId?: string;
  }

  /** 获取岗位与成员列表接口参数 */
  export interface GetColonizationMessageRoleListParams {
    campusId?: string;
  }

  /** 获取岗位与成员列表接口返回值 */
  export type GetColonizationMessageRoleListResult = Im.RoleInfo[];

  /** 获取学校列表接口返回值 */
  export type GetColonizationMessageSchoolListResult = Im.SchoolInfo[];

  /** 创建群接口参数 */
  export interface CreateGroupParams {
    /** 学生id */
    studentId?: string;
    /** 班级id */
    clazzId?: string;
    /** 群名称 */
    groupName: string;
    /** 群类型：1班级群 2自定义群组 3私聊 */
    groupType: 1 | 2 | 3;
    /** 群头像地址 */
    faceUrl: string;
    /** 群简介 */
    introduction?: string;
    /** 群自定义字段 */
    dataList: Im.GroupDataItem[];
    /** 群成员列表 */
    memberList: Im.GroupMemberInfo[];
  }

  /** 创建群接口返回值 */
  export type CreateGroupResult = string;

  /** 添加申请加入群接口参数 */
  export type AddJoinGroupRelateParams = Im.AddJoinGroupRelateParams;

  /** 添加申请加入群接口返回值 */
  export type AddJoinGroupRelateResult = string;

  /** 分页查询申请入群记录参数 */
  export type GetJoinGroupPageParams = Im.GetJoinGroupPageParams;

  /** 分页查询申请入群记录结果 */
  export type GetJoinGroupPageResult = Im.GetJoinGroupPageResult;

  /** 更新申请入群记录状态参数 */
  export type UpdateJoinGroupRelateStateParams =
    Im.UpdateJoinGroupRelateStateParams;

  /** 更新申请入群记录状态返回值 */
  export type UpdateJoinGroupRelateStateResult = string;
}

/**
 * 获取行政班成员列表
 */
export async function getAdministrativeClassMemberListApi(
  params: ImGroupManagementApi.GetAdministrativeClassMemberListParams,
) {
  return requestClient.get<ImGroupManagementApi.GetAdministrativeClassMemberListResult>(
    '/im/group/management/getAdministrativeClassMemberList',
    {
      params,
    },
  );
}

/**
 * 获取行政班列表
 */
export async function getAdministrativeClassListApi(
  params: ImGroupManagementApi.GetAdministrativeClassListParams,
) {
  return requestClient.get<ImGroupManagementApi.GetAdministrativeClassListResult>(
    '/im/group/management/getAdministrativeClassList',
    {
      params,
    },
  );
}

/**
 * 获取岗位与成员列表
 */
export async function getColonizationMessageRoleListApi(
  params: ImGroupManagementApi.GetColonizationMessageRoleListParams,
) {
  return requestClient.get<ImGroupManagementApi.GetColonizationMessageRoleListResult>(
    '/im/group/management/getColonizationMessageRoleList',
    {
      params,
    },
  );
}

/**
 * 获取学校列表
 */
export async function getColonizationMessageSchoolListApi() {
  return requestClient.get<ImGroupManagementApi.GetColonizationMessageSchoolListResult>(
    '/im/group/management/getColonizationMessageSchoolList',
  );
}

/**
 * 创建群
 */
export async function createGroupApi(
  params: ImGroupManagementApi.CreateGroupParams,
) {
  return requestClient.post<ImGroupManagementApi.CreateGroupResult>(
    '/im/group/management/createGroup',
    params,
  );
}

/**
 * 添加申请加入群
 */
export async function addJoinGroupRelateApi(
  params: ImGroupManagementApi.AddJoinGroupRelateParams,
) {
  return requestClient.post<ImGroupManagementApi.AddJoinGroupRelateResult>(
    '/im/group/management/addJoinGroupRelate',
    params,
  );
}

/**
 * 获取入群申请列表
 */
export async function getJoinGroupPageApi(
  params: ImGroupManagementApi.GetJoinGroupPageParams,
) {
  return requestClient.get<ImGroupManagementApi.GetJoinGroupPageResult>(
    '/im/group/management/getJoinGroupPage',
    {
      params,
    },
  );
}

/**
 * 修改状态
 */
export async function updateJoinGroupRelateStateApi(
  params: ImGroupManagementApi.UpdateJoinGroupRelateStateParams,
) {
  return requestClient.post<ImGroupManagementApi.UpdateJoinGroupRelateStateResult>(
    '/im/group/management/updateJoinGroupRelateState',
    params,
  );
}
