# 添加申请加入群

## 接口信息

- **请求方法**：POST
- **接口地址**：/im/group/management/addJoinGroupRelate

#### 请求头部

#### 请求参数

| 字段          | 类型   | 是否必传 | 描述                               |
| ------------- | ------ | -------- | ---------------------------------- |
| studentId     | string | 否       | 学生id                             |
| joinGroupName | string | 否       | 名称                               |
| groupId       | string | 是       | 群id                               |
| shareId       | string | 否       | 分享者IM编号                       |
| joinGroupType | number | 是       | 1-搜索群号入群 2-群内分享 3-二维码 |
| remark        | string | 否       | 备注                               |

#### 请求示例

```json
{
  "studentId": "",
  "joinGroupName": "",
  "groupId": "10000",
  "shareId": "",
  "joinGroupType": 1,
  "remark": "你好我是"
}
```

#### 响应参数

| 字段 | 类型   | 是否必传 | 描述 |
| ---- | ------ | -------- | ---- |
| data | string | 是       | -    |

#### 响应示例

```json
""
```

#### 详细说明

studentId有值就是学生好友列表，或者就是当前登录的好友

1. 通过私聊邀请的免审核，被邀请通过后直接进入新的聊天;
2. 通过群聊邀需要走审核流程;
3. 搜索申请入群走审核流程
