import type { Im } from '@amdox/types';

import { ref } from 'vue';

import {
  getAdministrativeClassListApi,
  getAdministrativeClassMemberListApi,
  getColonizationMessageRoleListApi,
} from '#/api/im/group-management';

/**
 * 组织架构数据管理 Hook
 */
export function useOrganization() {
  // 分别存储班级列表和岗位列表
  const classList = ref<Im.ClassInfo[]>([]);
  const roleList = ref<Im.RoleInfo[]>([]);
  const classListLoading = ref<boolean>(false);
  const roleListLoading = ref<boolean>(false);

  // 添加标志来防止无限循环
  const hasAttemptedClassLoad = ref<boolean>(false);
  const hasAttemptedRoleLoad = ref<boolean>(false);

  /**
   * 获取班级列表数据
   * @param forceRefresh 是否强制刷新数据，默认为 false
   */
  async function getClassListData(forceRefresh: boolean = false) {
    console.warn('[useOrganization] getClassListData called', {
      forceRefresh,
      hasAttemptedClassLoad: hasAttemptedClassLoad.value,
      isLoading: classListLoading.value,
    });

    // 如果已经尝试过加载且不是强制刷新，则不再重复请求
    if (hasAttemptedClassLoad.value && !forceRefresh) {
      console.warn(
        '班级数据已尝试加载过，避免重复请求。如需强制刷新，请设置 forceRefresh 为 true',
      );
      return classList.value;
    }

    // 如果正在加载中，避免重复请求
    if (classListLoading.value && !forceRefresh) {
      console.warn('班级数据正在加载中，避免重复请求');
      return classList.value;
    }

    try {
      classListLoading.value = true;
      hasAttemptedClassLoad.value = true;

      console.warn('[useOrganization] 开始获取班级列表数据...');
      const classListRes = await getAdministrativeClassListApi({});
      classList.value = classListRes || [];

      if (classList.value.length === 0) {
        console.warn('班级列表数据为空');
      } else {
        console.warn(
          `班级列表数据加载成功，共 ${classList.value.length} 个班级`,
        );
      }

      return classList.value;
    } catch (error) {
      console.error('获取班级列表失败:', error);
      classList.value = [];
      return [];
    } finally {
      classListLoading.value = false;
      console.warn('[useOrganization] 班级列表数据加载完成');
    }
  }

  /**
   * 获取岗位列表数据
   * @param forceRefresh 是否强制刷新数据，默认为 false
   */
  async function getRoleListData(forceRefresh: boolean = false) {
    console.warn('[useOrganization] getRoleListData called', {
      forceRefresh,
      hasAttemptedRoleLoad: hasAttemptedRoleLoad.value,
      isLoading: roleListLoading.value,
    });

    // 如果已经尝试过加载且不是强制刷新，则不再重复请求
    if (hasAttemptedRoleLoad.value && !forceRefresh) {
      console.warn(
        '岗位数据已尝试加载过，避免重复请求。如需强制刷新，请设置 forceRefresh 为 true',
      );
      return roleList.value;
    }

    try {
      roleListLoading.value = true;
      hasAttemptedRoleLoad.value = true;

      const roleListRes = await getColonizationMessageRoleListApi({});
      roleList.value = roleListRes || [];

      if (roleList.value.length === 0) {
        console.warn('岗位列表数据为空');
      }

      return roleList.value;
    } catch (error) {
      console.error('获取岗位列表失败:', error);
      roleList.value = [];
      return [];
    } finally {
      roleListLoading.value = false;
    }
  }

  /**
   * 重置加载状态，允许重新加载数据
   */
  function resetLoadState() {
    hasAttemptedClassLoad.value = false;
    hasAttemptedRoleLoad.value = false;
  }

  /**
   * 获取班级成员列表
   * @param clazzId 班级ID
   * @returns 班级成员列表
   */
  async function getClassMembers(clazzId: string) {
    try {
      console.warn(`[useOrganization] 开始获取班级 ${clazzId} 的成员列表...`);
      const data = await getAdministrativeClassMemberListApi({ clazzId });

      const members: any[] = [];

      // 添加学生
      if (data.studentList) {
        members.push(
          ...data.studentList.map((student: Im.UserInfo) => ({
            userID: student.imUserId,
            nick: student.userName,
            avatar: student.userIconPath,
            isDisabled: false,
            isGroupHeader: false,
            userType: 'student',
            clazzId,
          })),
        );
      }

      // 添加教师
      if (data.teacherList) {
        members.push(
          ...data.teacherList.map((teacher: Im.UserInfo) => ({
            userID: teacher.imUserId,
            nick: teacher.userName,
            avatar: teacher.userIconPath,
            isDisabled: false,
            isGroupHeader: false,
            userType: 'teacher',
            clazzId,
          })),
        );
      }

      // 添加家长
      if (data.patriarchList) {
        members.push(
          ...data.patriarchList.map((patriarch: Im.PatriarchInfo) => ({
            userID: patriarch.imUserId,
            nick: `${patriarch.userName}(${patriarch.studentName}${patriarch.relateName})`,
            avatar: patriarch.userIconPath,
            isDisabled: false,
            isGroupHeader: false,
            userType: 'patriarch',
            clazzId,
          })),
        );
      }

      console.warn(
        `[useOrganization] 班级 ${clazzId} 成员列表加载成功，共 ${members.length} 个成员`,
      );
      return members;
    } catch (error) {
      console.error(`获取班级 ${clazzId} 成员失败:`, error);
      return [];
    }
  }

  return {
    // 状态
    classList,
    roleList,
    classListLoading,
    roleListLoading,

    // 方法
    getClassListData,
    getRoleListData,
    getClassMembers,
    resetLoadState,
  };
}
