interface BasicOption {
  label: string;
  value: string;
}

type SelectOption = BasicOption;

type TabOption = BasicOption;

interface BasicUserInfo {
  // 保留原有字段以兼容现有代码
  /**
   * 头像 (兼容字段，映射到 userIconPath)
   */
  avatar?: string;
  /**
   * 科目信息
   */
  courseName: string;
  /**
   * 访客性别
   */
  gender: string;
  /**
   * 用户昵称 (兼容字段，映射到 userName)
   */
  realName?: string;
  /**
   * 用户多角色，逗号分隔
   */
  roleList: string[];
  /**
   * 用户最高角色
   */
  roleName: string;
  /**
   * 用户角色 (兼容字段，映射到 roleList)
   */
  roles?: string[];
  /**
   * 学校ID
   */
  schoolId: string;
  /**
   * 手机号
   */
  telephone: string;
  /**
   * 用户头像存放路径
   */
  userIconPath: string;

  /**
   * 用户编号
   */
  userId: string;
  /**
   * 操作人/用户名
   */
  userName: string;
  /**
   * 用户名 (兼容字段，映射到 userName)
   */
  username?: string;
  /**
   * 用户状态
   */
  userState: string;
}

type ClassType = Array<object | string> | object | string;

export type { BasicOption, BasicUserInfo, ClassType, SelectOption, TabOption };
