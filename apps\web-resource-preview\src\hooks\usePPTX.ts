import type { PPTXPreview } from 'node_modules/@amdox/webview/src/types/abstract/resourcePreviewAbstract';

import type { Ref } from 'vue';

import { ref } from 'vue';

import html2canvas from 'html2canvas';

import { downloadBase64 } from '#/utils/file';

export async function useCreatePPTXPreview(
  handleHiddenLoading: () => void,
  isDownload = false,
): Promise<Ref<PPTXPreview[]>> {
  const previewList = ref<PPTXPreview[]>([]);
  // 预览区域
  const previewContainer = document.querySelector(
    '.preview-container',
  ) as HTMLElement;
  // pptx每页的元素
  const pptxSliderEls = document.querySelectorAll(
    '.pptx-preview-slide-wrapper',
  );

  const templateDiv = document.createElement('div');
  templateDiv.style.position = 'absolute';
  templateDiv.style.zIndex = '-5';
  previewContainer?.append(templateDiv);

  if (pptxSliderEls && typeof pptxSliderEls[Symbol.iterator] === 'function') {
    // isLoading.value = true;

    try {
      let index = 1;
      for (const el of pptxSliderEls) {
        const newEl = el.cloneNode(true);
        if (newEl instanceof HTMLElement) {
          newEl.style.position = 'none';
          newEl.style.transform = 'none';
          newEl.style.left = '0';
          newEl.style.top = '0';
          templateDiv.append(newEl);
          const width = newEl.offsetWidth;
          const height = newEl.offsetHeight;
          const customCanvas = document.createElement('canvas');
          const scale = 2;
          customCanvas.width = width * scale;
          customCanvas.height = height * scale;
          const canvas = await html2canvas(newEl as HTMLElement, {
            backgroundColor: null,
            useCORS: true,
            canvas: customCanvas,
            width,
            height,
            scale,
          });
          const context = canvas.getContext('2d');
          if (context) {
            context.imageSmoothingEnabled = false;
            const base64 = canvas.toDataURL('image/png');
            const tempIndex = index;
            index++;
            const fileName = `${tempIndex}_图片_${Date.now()}`;
            previewList.value.push({
              base64,
              fileName,
            });
            if (isDownload) {
              downloadBase64(base64, fileName);
            }
          }
        }
      }
    } catch (error) {
      console.error('获取图片失败:', error);
    } finally {
      handleHiddenLoading();
      templateDiv?.remove();
    }
    handleHiddenLoading();
    templateDiv?.remove();
  }
  return previewList;
}
