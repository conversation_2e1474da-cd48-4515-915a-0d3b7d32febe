.TUI-profile-h5 {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
  background: #efefef;

  &-basic {
    box-sizing: border-box;
    flex-direction: row;
    align-items: flex-start;
    width: 100%;
    padding: 14px 18px;
    margin-bottom: 10px;
    background: #fff;

    &-avatar {
      width: 78px;
      height: 78px;
      border-radius: 8px;
    }

    &-info {
      &-nick {
        flex: 1;
        padding: 6px 0;
        overflow: hidden;
        text-overflow: ellipsis;
        font-family: PingFangSC-Medium;
        font-size: 14px;
        font-weight: 500;
        color: #000;
        letter-spacing: 0;
        word-break: keep-all;
        white-space: nowrap;
      }

      &-id {
        padding: 6px 0;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        font-weight: 400;
        color: #999;
        letter-spacing: 0;
        word-break: keep-all;

        &-value {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  &-setting {
    flex-direction: column;
    width: 100%;

    &-item {
      padding: 10px;
      margin-bottom: 10px;
      background: #fff;

      &-exit {
        .TUI-profile-h5-setting-item-label {
          justify-content: center;

          .label-left {
            .label-title {
              color: red;
            }
          }
        }
      }

      &-label {
        .label-left {
          display: flex;
          flex-direction: column;

          .label-title {
            font-size: 14px;
            color: #444;
          }
        }

        .label-right {
          display: flex;
          flex-direction: row;
          font-size: 14px;
          color: #000;
        }
      }

      &-bottom-popup {
        padding: 10px;
        font-size: 16px;
        color: #147aff;
        border-bottom: 1px solid #dbdbdb;
      }
    }
  }
}
