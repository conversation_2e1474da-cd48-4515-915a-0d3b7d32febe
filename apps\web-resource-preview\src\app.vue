<script lang="ts" setup>
import { onMounted } from 'vue';

import { useElementPlusDesignTokens } from '@amdox/hooks';

import { ElConfigProvider } from 'element-plus';

import { elementLocale } from '#/locales';
import { setupWebviewEnv } from '#/utils/webview/setup';

defineOptions({ name: 'App' });

useElementPlusDesignTokens();

onMounted(async () => {
  await setupWebviewEnv();
});
</script>

<template>
  <ElConfigProvider :locale="elementLocale">
    <RouterView />
  </ElConfigProvider>
</template>
