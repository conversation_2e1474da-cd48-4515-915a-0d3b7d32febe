import { initPreferences } from '@amdox/preferences';
import { unmountGlobalLoading } from '@amdox/utils';

import { overridesPreferences } from './preferences';

// eslint-disable-next-line no-extend-native
String.prototype.replaceAll = function (search: any, replacement: any) {
  // 处理正则表达式
  if (search instanceof RegExp) {
    if (!search.global) {
      throw new TypeError(
        'replaceAll called with a non-global RegExp argument',
      );
    }
    return this.replace(search, replacement);
  }

  // 处理字符串
  return this.replaceAll(new RegExp(search, 'g'), replacement);
};

/**
 * 应用初始化完成之后再进行页面加载渲染
 */
async function initApplication() {
  // name用于指定项目唯一标识
  // 用于区分不同项目的偏好设置以及存储数据的key前缀以及其他一些需要隔离的数据
  const env = import.meta.env.PROD ? 'prod' : 'dev';
  const appVersion = import.meta.env.VITE_APP_VERSION;
  const namespace = `${import.meta.env.VITE_APP_NAMESPACE}-${appVersion}-${env}`;

  // app偏好设置初始化
  await initPreferences({
    namespace,
    overrides: overridesPreferences,
  });

  // 启动应用并挂载
  // vue应用主要逻辑及视图
  const { bootstrap } = await import('./bootstrap');
  await bootstrap(namespace);

  // 移除并销毁loading
  unmountGlobalLoading();
}

initApplication();
