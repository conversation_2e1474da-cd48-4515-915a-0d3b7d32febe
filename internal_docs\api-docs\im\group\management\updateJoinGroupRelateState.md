# 修改状态

## 接口信息

- **请求方法**：POST
- **接口地址**：/im/group/management/updateJoinGroupRelateState

#### 请求参数

| 字段 | 类型 | 是否必传 | 描述 |
| --- | --- | --- | --- |
| studentId | string | Yes | 学生id |
| joinGroupState | number | Yes | 1-待审核 2审核通过 3驳回申请（默认值：2） |
| id | string | Yes | 主键(列表编号) |

#### 请求示例

```json
{
  "studentId": "", // 学生id
  "joinGroupState": 2, // 1-待审核 2审核通过 3驳回申请
  "id": "5" // 主键(列表编号)
}
```

#### 响应参数

| 字段 | 类型   | 是否必传 | 描述 |
| ---- | ------ | -------- | ---- |
| data | string | Yes      | -    |

#### 响应示例

```json
""
```

#### 详细说明

studentId有值就是学生好友列表，或者就是当前登录的好友
