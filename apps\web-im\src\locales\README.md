# locale - 国际化

每个app使用的国际化可能不同，这里用于扩展国际化的功能，例如扩展 dayjs、antd组件库的多语言切换，以及app本身的国际化文件。

## 目录结构

```
locales/
├── langs/                    # 多语言文件目录
│   ├── ar-SA/               # 阿拉伯语（沙特阿拉伯）
│   │   └── page.json        # 页面翻译文件
│   ├── bn-BD/               # 孟加拉语（孟加拉国）
│   │   └── page.json
│   ├── en-US/               # 英语（美国）
│   │   └── page.json
│   ├── es-ES/               # 西班牙语（西班牙）
│   │   └── page.json
│   ├── fr-FR/               # 法语（法国）
│   │   └── page.json
│   ├── hi-IN/               # 印地语（印度）
│   │   └── page.json
│   ├── id-ID/               # 印尼语（印度尼西亚）
│   │   └── page.json
│   ├── ja-JP/               # 日语（日本）
│   │   └── page.json
│   ├── ko-KR/               # 韩语（韩国）
│   │   └── page.json
│   ├── ms-MY/               # 马来语（马来西亚）
│   │   └── page.json
│   ├── ru-RU/               # 俄语（俄罗斯）
│   │   └── page.json
│   ├── th-TH/               # 泰语（泰国）
│   │   └── page.json
│   ├── vi-VN/               # 越南语（越南）
│   │   └── page.json
│   └── zh-CN/               # 中文（简体）
│       └── page.json
└── README.md                # 本文档
```

## 支持的语言

本项目支持以下14种语言：

| 语言代码 | 语言名称         | 地区       |
| -------- | ---------------- | ---------- |
| ar-SA    | العربية          | 沙特阿拉伯 |
| bn-BD    | বাংলা            | 孟加拉国   |
| en-US    | English          | 美国       |
| es-ES    | Español          | 西班牙     |
| fr-FR    | Français         | 法国       |
| hi-IN    | हिन्दी           | 印度       |
| id-ID    | Bahasa Indonesia | 印度尼西亚 |
| ja-JP    | 日本語           | 日本       |
| ko-KR    | 한국어           | 韩国       |
| ms-MY    | Bahasa Melayu    | 马来西亚   |
| ru-RU    | Русский          | 俄罗斯     |
| th-TH    | ไทย              | 泰国       |
| vi-VN    | Tiếng Việt       | 越南       |
| zh-CN    | 中文（简体）     | 中国       |

## 翻译文件结构

每个语言目录下的 `page.json` 文件包含该语言的翻译内容，结构示例：

```json
{
  "preview": {
    "title": "预览"
  },
  "test": {
    "tuikit_integration": "TUIKit 语言包集成测试",
    "message": "这是一个测试消息"
  }
}
```

## 使用说明

1. **添加新语言**：在 `langs/` 目录下创建新的语言代码目录，并添加相应的翻译文件
2. **修改翻译**：直接编辑对应语言目录下的 `page.json` 文件
3. **扩展翻译内容**：在 JSON 文件中添加新的翻译键值对，保持所有语言文件的结构一致
