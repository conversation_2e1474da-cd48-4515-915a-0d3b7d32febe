import type { BasicUserInfo } from '@amdox/types';

import { createApp, watchEffect } from 'vue';

import { TUIChatKit, TUIComponents } from '@amdox/chat-ui';
import TUINotification from '@amdox/chat-ui/components/TUINotification/index';
// 导入 TUIKit 的语言包
import { registerLoadingDirective } from '@amdox/common-ui';
import { preferences } from '@amdox/preferences';
import { initStores, useAccessStore, useUserStore } from '@amdox/stores';
import '@amdox/styles';
import '@amdox/styles/ele';

import { StoreName, TUIStore } from '@tencentcloud/chat-uikit-engine';
import { useTitle } from '@vueuse/core';
import { ElLoading } from 'element-plus';

import { $t, setupI18n } from '#/locales';

import { initComponentAdapter } from './adapter/component';
import App from './app.vue';
import { router } from './router';

async function bootstrap(namespace: string) {
  // 初始化组件适配器
  await initComponentAdapter();

  // // 设置弹窗的默认配置
  // setDefaultModalProps({
  //   fullscreenButton: false,
  // });
  // // 设置抽屉的默认配置
  // setDefaultDrawerProps({
  //   zIndex: 2000,
  // });
  const app = createApp(App);

  // 注册Element Plus提供的v-loading指令
  app.directive('loading', ElLoading.directive);

  // 注册Vben提供的v-loading和v-spinning指令
  registerLoadingDirective(app, {
    loading: false, // Vben提供的v-loading指令和Element Plus提供的v-loading指令二选一即可，此处false表示不注册Vben提供的v-loading指令
    spinning: 'spinning',
  });

  // 国际化 i18n 配置
  await setupI18n(app);

  // 配置 pinia-tore
  await initStores(app, { namespace });

  // 初始化 tippy
  const { initTippy } = await import('@amdox/common-ui/es/tippy');
  initTippy(app);

  // 配置路由及路由守卫
  app.use(router);

  // 配置Motion插件
  const { MotionPlugin } = await import('@amdox/plugins/motion');
  app.use(MotionPlugin);

  // 动态更新标题
  watchEffect(() => {
    if (preferences.app.dynamicTitle) {
      const routeTitle = router.currentRoute.value.meta?.title;
      const pageTitle =
        (routeTitle ? `${$t(routeTitle)} - ` : '') + preferences.app.name;
      useTitle(pageTitle);
    }
  });

  app.mount('#app');

  TUIChatKit.components(TUIComponents, app);
  TUIChatKit.init();

  /**
   * Init TUINotification configuration.
   */
  TUINotification.setNotificationConfiguration({
    showPreviews: true,
    allowNotifications: true,
    notificationTitle: 'Tencent Cloud Chat',
    notificationIcon:
      'https://web.sdk.qcloud.com/im/demo/latest/faviconnew.png',
  });

  /**
   * Listen for new messages and use notification components.
   * This capability is only available in the web environment.
   */
  TUIStore.watch(StoreName.CHAT, {
    newMessageList: (newMessageList: unknown) => {
      if (Array.isArray(newMessageList)) {
        newMessageList.forEach((message) => TUINotification.notify(message));
      }
    },
  });

  afterBootstrap();
}

const afterBootstrap = () => {
  if (
    import.meta.env.DEV &&
    import.meta.env.VITE_MOCK_DATA_ENV !== 'client_debug'
  ) {
    const accessStore = useAccessStore();
    const userStore = useUserStore();
    const userInfo: BasicUserInfo = {
      /** 用户编号 */
      userId: import.meta.env.VITE_MOCK_USER_ID,
      /** 访客性别 */
      gender: '',
      /** 手机号 */
      telephone: import.meta.env.VITE_MOCK_USER_TELEPHONE,
      /** 操作人 */
      userName: import.meta.env.VITE_MOCK_USER_USERNAME,
      /** 用户头像存放路径 */
      userIconPath: import.meta.env.VITE_MOCK_USER_AVATAR_URL,
      /** 用户最高角色 */
      roleName: '',
      /** 用户多角色，逗号分隔 */
      roleList: [],
      /** 用户状态 */
      userState: '',
      // 科目信息
      courseName: '',
      // 学校ID
      schoolId: import.meta.env.VITE_MOCK_CAMPUS_ID,
    };
    userStore.setUserInfo(userInfo);
    accessStore.setAccessToken(import.meta.env.VITE_MOCK_ACCESS_TOKEN);
    accessStore.setOldAccessToken(import.meta.env.VITE_MOCK_OLD_ACCESS_TOKEN);
  }
};

export { bootstrap };
