<script lang="ts" setup>
import VueOfficeDocx from '@vue-office/docx';

import { useHiddenLoading } from '#/hooks/useLoading';

import '@vue-office/docx/lib/index.css'; // 引入样式

// 定义组件名称
defineOptions({ name: 'DocumentPreview' });

// 定义 props
defineProps<{ src: string }>();

const emits = defineEmits<{
  (e: 'loading'): void;
}>();

const { handleHiddenLoading } = useHiddenLoading(emits);

const rendered = () => {
  // eslint-disable-next-line no-console
  console.log('document rendered'); // 可以添加一些区分信息
  handleHiddenLoading();
};

const errorHandler = (error: any) => {
  // eslint-disable-next-line no-console
  console.log('document error', error);
};
</script>

<template>
  <VueOfficeDocx
    :src="src"
    style="height: 100vh"
    @rendered="rendered"
    @error="errorHandler"
  />
</template>
