/**
 * 全局类型声明
 */
declare global {
  interface Window {
    uni?: any;
    wx?: any;
  }

  // const wx: any;
  // const uni: any;
}

/**
 * 平台类型
 */
export type PlatformType = '' | 'app' | 'h5' | 'pc' | 'wechat';

/**
 * 检测是否为微信小程序环境
 */
function isWechatMiniProgram(): boolean {
  return (
    typeof wx !== 'undefined' &&
    typeof wx.getSystemInfoSync === 'function' &&
    Boolean(wx.getSystemInfoSync().fontSizeSetting)
  );
}

/**
 * 检测是否为 uni-app 环境
 */
function isUniApp(): boolean {
  return typeof uni !== 'undefined' && typeof window === 'undefined';
}

/**
 * 检测是否为浏览器环境
 */
function isBrowser(): boolean {
  return (
    (typeof uni !== 'undefined' || typeof window !== 'undefined') &&
    !isWechatMiniProgram() &&
    !isUniApp()
  );
}

/**
 * 获取用户代理字符串
 */
function getUserAgent(): string {
  if (isBrowser() && typeof window !== 'undefined' && window.navigator) {
    return window.navigator.userAgent || '';
  }
  return '';
}

/**
 * 检测是否为移动设备
 */
function isMobileDevice(): boolean {
  const userAgent = getUserAgent();

  // Android 设备
  const isAndroid = /Android/i.test(userAgent);

  // Windows Phone 设备
  const isWindowsPhone = /Windows Phone/.test(userAgent);

  // SymbianOS 设备
  const isSymbianOS = /SymbianOS/.test(userAgent);

  // OpenHarmony 设备
  const isOpenHarmony = /OpenHarmony/i.test(userAgent);

  // iOS 设备
  const isIOS = /iPhone/i.test(userAgent) || /iPod/i.test(userAgent);

  // 平板设备检测
  const isTablet =
    isBrowser() &&
    typeof window !== 'undefined' &&
    ('ontouchstart' in window || navigator.maxTouchPoints > 0) &&
    (() => {
      if (isBrowser() && typeof window !== 'undefined') {
        if (/Tablet|Pad/i.test(userAgent)) return true;

        const { innerHeight, innerWidth, orientation } = window;
        if (orientation === 180 || orientation === 0) {
          return innerWidth >= 768 && innerHeight >= 1024;
        }
        if (orientation === 90 || orientation === -90) {
          return innerWidth >= 1024 && innerHeight >= 768;
        }
      }
      return false;
    })();

  return (
    isAndroid ||
    isWindowsPhone ||
    isSymbianOS ||
    isIOS ||
    isTablet ||
    isOpenHarmony
  );
}

/**
 * 获取当前运行平台
 * @returns 平台类型：'pc' | 'h5' | 'wechat' | 'app' | ''
 */
export function getPlatform(): PlatformType {
  const isWechat = isWechatMiniProgram();
  const isUni = isUniApp();
  const isBrowserEnv = isBrowser();
  const isMobile = isMobileDevice();

  // PC 浏览器环境：在浏览器中且非移动设备
  const isPc = isBrowserEnv && !isMobile;

  // 移动设备环境：移动设备
  const isH5 = isMobile;

  // uni-app App 环境：在 uni-app 中且非微信小程序
  const isApp = isUni && !isWechat;

  if (isPc) return 'pc';
  if (isH5) return 'h5';
  if (isWechat) return 'wechat';
  if (isApp) return 'app';

  return '';
}

/**
 * 检测是否为 iOS 设备
 */
export function isIOS(): boolean {
  const userAgent = getUserAgent();
  return /iPhone/i.test(userAgent) || /iPod/i.test(userAgent);
}

/**
 * 获取全局对象
 */
export function getGlobalThis(): any {
  if (isWechatMiniProgram()) return wx;
  if (typeof uni !== 'undefined') return uni;
  if (typeof window !== 'undefined') return window;
  return {};
}
