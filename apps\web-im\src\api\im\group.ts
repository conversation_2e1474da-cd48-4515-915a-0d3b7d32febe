import type { Im } from '@amdox/types';

import { requestClient } from '#/api/request';

export namespace ImGroupApi {
  /** 获取群列表接口参数 */
  export interface GetGroupListParams {
    /** 学生编号 */
    studentId?: string;
  }

  /** 获取群列表接口返回值 */
  export type GetGroupListResult = Im.GroupInfo[];

  /** 获取群详情接口参数 */
  export interface GetGroupInfoParams {
    /** 学生编号 */
    studentId?: string;
    /** 群编号 */
    groupId: string;
  }

  /** 获取群详情接口返回值 */
  export interface GetGroupInfoResult extends Im.GroupInfo {
    /** true 已加入群，false 未加入群 */
    whetherJoin: boolean;
    /** 群主或班主任（搜索使用） */
    ownerName: string;
  }
}

/**
 * 获取群列表
 */
export async function getGroupListApi(params?: ImGroupApi.GetGroupListParams) {
  return requestClient.post<ImGroupApi.GetGroupListResult>(
    '/im/group/getGroupList',
    params || {},
  );
}

/**
 * 获取群详情
 */
export async function getGroupInfoApi(params: ImGroupApi.GetGroupInfoParams) {
  return requestClient.post<ImGroupApi.GetGroupInfoResult>(
    '/im/group/getGroupInfo',
    params,
  );
}

/**
 * 更新群信息
 */
export async function updateGroupInfoApi(params: Im.UpdateGroupInfoParams) {
  return requestClient.post<any>('/im/group/updateGroupInfo', params);
}
