import { requestClient } from '#/api/request';

export namespace ImUserApi {
  /** 获取IM账号信息接口参数 */
  export interface GetIMUserInfoParams {
    studentId?: string;
  }

  /** 获取IM账号信息接口返回值 */
  export interface GetIMUserInfoResult {
    content: string;
    offset: string;
  }

  /** 批量添加IM账号信息接口参数 */
  export interface AddIMUserParams {
    imUserId: string;
    userName: string;
    userIconPath: string;
  }
}

/**
 * 获取IM账号信息
 * 参数学生编号有值，就是获取学生的im编号，否则获取用户自身的im编号
 */
export async function getIMUserInfoApi(params?: ImUserApi.GetIMUserInfoParams) {
  return requestClient.get<ImUserApi.GetIMUserInfoResult>(
    '/im/user/getIMUserInfo',
    {
      params,
    },
  );
}

/**
 * 批量添加IM账号信息
 */
export async function addIMUserApi(params: ImUserApi.AddIMUserParams[]) {
  return requestClient.post('/im/user/addIMUser', params);
}
