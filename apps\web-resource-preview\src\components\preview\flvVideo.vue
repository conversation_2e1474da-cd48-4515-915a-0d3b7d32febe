<script lang="ts" setup>
import { nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue';

import flvjs from 'flv.js';

const props = defineProps<{ src: string }>();

const videoRef = ref<HTMLVideoElement | null>(null);
const flvPlayer = ref();
let bufferCleanInterval: any = null;

// const loadFlv = (url) => {
//   if (!isValidUrl(url)) {
//     return;
//   }
//   if(flvjs.isSupported()) {
//     try{
//       nextTick(()=> {
//         flvPlayer.value = flvjs.createPlayer({
//           type: "flv",
//           url: props.src|| "",
//           isLive:false,
//           hasAudio:false,
//           hasVideo:true,
//           enableStashBuffer:false,
//           stashInitialSize: 128*1024

//         });
//       })
//     }
//   }
// };
// const isValidUrl = (urlString: string): boolean => {
//   try {
//     const url = new URL(urlString);
//     return true;
//   } catch {
//     return false;
//   }
// };
const loadFlv = (url: string) => {
  // if (!isValidUrl(url)) {
  //   console.error('Invalid URL provided');
  //   return;
  // }
  if (flvjs.isSupported()) {
    try {
      nextTick(() => {
        flvPlayer.value = flvjs.createPlayer(
          {
            type: 'flv',
            url: url || '',
            isLive: false,
            hasAudio: false,
            hasVideo: true,
            // enableStashBuffer: false,
            // 减少缓冲区大小
            // stashInitialSize: 128 * 1024,
          },
          {
            enableWorker: false,
            enableStashBuffer: false,
            autoCleanupSourceBuffer: true,
            // 调整缓冲区清理阈值
            // maxBufferSize: 1024 * 1024,
            // maxBufferDuration: 3,
          },
        );
        flvPlayer.value.attachMediaElement(videoRef.value);
        // 监听加载错误事件
        flvPlayer.value.on(flvjs.Events.ERROR, (type: any, info: any) => {
          console.error('FLV player error:', type, info);
          // openMessages("error", "视频加载失败，请检查网络或URL");
        });
        flvPlayer.value.load();
        flvPlayer.value.play();
        // eslint-disable-next-line no-console
        console.log('FLV player loaded and started successfully');
        // 定期清理缓冲区
        bufferCleanInterval = setInterval(() => {
          if (flvPlayer.value) {
            flvPlayer.value.cleanupSourceBuffer();
          }
        }, 5000);
      });
    } catch (error) {
      console.error('Error initializing FLV player:', error);
    }
  } else {
    console.error('FLV.js is not supported in this browser');
  }
};

const destroyPlayer = () => {
  if (flvPlayer.value) {
    flvPlayer.value.pause();
    flvPlayer.value.unload();
    flvPlayer.value.detachMediaElement();
    flvPlayer.value.destroy();
    flvPlayer.value = null;
  }
  if (bufferCleanInterval) {
    clearInterval(bufferCleanInterval);
  }
};

onMounted(() => {
  loadFlv(props.src);
  // const video = videoRef.value;
  // if (!video) return;
  // video.src = props.src;
  // video.play();
});

onBeforeUnmount(() => {
  destroyPlayer();
});

watch(
  () => props.src,
  (val) => {
    destroyPlayer();
    loadFlv(val);
  },
  { deep: true, immediate: true },
);
</script>
<template>
  <div class="player">
    <video ref="videoRef" autoplay controls muted></video>
  </div>
</template>
<style lang="scss" scoped></style>
