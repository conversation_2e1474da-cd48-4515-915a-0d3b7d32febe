import { ref } from 'vue';

import { genTestUserSig } from '@amdox/chat-ui';
import { useUserStore } from '@amdox/stores';

import { decryptBySecretIV } from '@amdox-core/shared/utils';

import {
  StoreName,
  TUIStore,
  TUIUserService,
} from '@tencentcloud/chat-uikit-engine';
import { TUILogin } from '@tencentcloud/tui-core';
import { ElMessage } from 'element-plus';
import { defineStore } from 'pinia';

import { addIMUserApi, getIMUserInfoApi } from '#/api/im/user';

export interface UseAutoLoginOptions {
  /** 手动指定的用户ID */
  // userID?: string;
  /** 是否显示消息提示 */
  showMessage?: boolean;
}

export const useAuthStore = defineStore(
  'auth',
  () => {
    const userStore = useUserStore();

    const loginLoading = ref(false);
    const imUserId = ref<null | string>(null);

    /**
     * 执行IM自动登录
     * @param options 登录选项
     */
    const autoIMLogin = async (options: UseAutoLoginOptions = {}) => {
      const { showMessage = true } = options;

      const userInfo = userStore.userInfo;
      console.warn('userInfo:', userInfo);

      if (!userInfo?.userId || !userInfo?.userName) {
        console.warn('用户信息不完整，无法注册IM账号。');
        if (showMessage) {
          ElMessage({
            message: '⚠️ 用户信息不完整，无法注册IM账号。',
            grouping: true,
            type: 'warning',
            duration: 3000,
          });
        }
        return false;
      }

      // 尝试注册IM账号
      try {
        const role = 'TEACHER';
        const prefix =
          import.meta.env.VITE_APP_TENCENT_IM_ENV === 'dev'
            ? `TEST_${role}_`
            : `${role}_`;
        await addIMUserApi([
          {
            imUserId: `${prefix}${userInfo.userId}`,
            userName: userInfo.userName,
            userIconPath: userInfo.userIconPath || '',
          },
        ]);
        console.warn('IM账号注册成功');
      } catch (error) {
        console.error('IM账号注册失败:', error);
        if (showMessage) {
          ElMessage({
            message: `❌ IM账号注册失败: ${error instanceof Error ? error.message : String(error)}`,
            grouping: true,
            type: 'error',
            duration: 5000,
          });
        }
        // 注册失败不中断后续流程，但需要考虑是否应该继续
      }

      // 获取腾讯云IM配置
      const { SDKAppID, secretKey } = getIMConfig();
      if (!SDKAppID || !secretKey) {
        const message = '腾讯云IM配置不完整，请检查环境变量配置';
        console.warn(message);
        if (showMessage) {
          ElMessage({
            message: `⚠️ ${message}`,
            grouping: true,
            type: 'warning',
            duration: 3000,
          });
        }
        return false;
      }

      try {
        const imUserInfo = await getIMUserInfo({
          SDKAppID,
          secretKey,
        });
        if (!imUserInfo) {
          throw new Error('无法获取IM用户信息');
        }

        const { userId, userSig } = imUserInfo;
        imUserId.value = userId;

        if (!userId || !userSig) {
          throw new Error('无法获取用户ID');
        }

        // 检查是否已有有效的登录信息
        if (await checkExistingIMLogin(userId, showMessage)) {
          return true;
        }

        // 显示正在登录的提示
        if (showMessage && import.meta.env.DEV) {
          ElMessage({
            message: `🔄 正在登录用户: ${userId}...`,
            grouping: true,
            type: 'info',
            duration: 2000,
          });
        }

        console.warn(`🚀 正在登录中... 用户ID: ${userId}`);

        // 准备登录信息
        const loginInfo = {
          SDKAppID,
          userID: userId,
          userSig,
          useUploadPlugin: true,
          framework: 'vue3',
        };

        // 执行登录
        await TUILogin.login(loginInfo);

        // 设置用户在线状态
        TUIUserService.switchUserStatus({ displayOnlineStatus: true });

        console.warn(`✅ 登录成功! 用户: ${userId}`);

        // 显示登录成功提示
        if (showMessage && import.meta.env.DEV) {
          ElMessage({
            message: `🎉 登录成功！欢迎回来，${userId}`,
            grouping: true,
            type: 'success',
            duration: 3000,
          });
        }

        return true;
      } catch (error) {
        console.error('❌ 自动登录失败:', error);

        // 显示详细的错误提示
        const errorMessage =
          error instanceof Error ? error.message : String(error);
        if (showMessage) {
          ElMessage({
            message: `❌ 登录失败: ${errorMessage}`,
            grouping: true,
            type: 'error',
            duration: 5000,
          });
        }
        return false;
      }
    };

    /**
     * 获取IM用户信息
     * 在开发环境下优先使用环境变量，生产环境下通过API获取
     */
    const getIMUserInfo = async ({
      SDKAppID,
      secretKey,
    }: {
      SDKAppID: number;
      secretKey: string;
    }): Promise<null | {
      userId: string;
      userSig: string;
    }> => {
      // 1. 开发环境下，优先使用环境变量中的自动登录用户ID
      if (
        import.meta.env.DEV &&
        import.meta.env.VITE_MOCK_DATA_IM_AUTO_LOGIN_USER_ID
      ) {
        const devUserID = import.meta.env.VITE_MOCK_DATA_IM_AUTO_LOGIN_USER_ID;
        if (devUserID) {
          // eslint-disable-next-line no-console
          console.log(`使用开发环境配置的用户ID: ${devUserID}`);
        }
        // 生成用户签名
        const options = genTestUserSig({
          SDKAppID,
          secretKey,
          userID: devUserID,
        });
        const userSig = options.userSig;
        return {
          userId: devUserID,
          userSig,
        };
      }

      // 2. 通过API获取IM用户信息
      try {
        const res = await getIMUserInfoApi();

        // eslint-disable-next-line no-console
        console.log('<<< IM User Info Response >>>', res);

        const dataStr = decryptBySecretIV(res.content, res.offset);
        const data = JSON.parse(dataStr);

        // eslint-disable-next-line no-console
        console.log('<<< IM User Info String >>>', dataStr);

        // eslint-disable-next-line no-console
        console.log('<<< IM User Info Object >>>', data);

        // 根据API响应结构获取用户ID
        const userID = data.imUserId;
        const userSig = data.userSig;

        if (userID && userSig) {
          return {
            userId: userID,
            userSig,
          };
        } else {
          console.warn('API响应中未找到有效的用户ID', res);
          return null;
        }
      } catch (error) {
        console.error('获取用户ID失败:', error);
        return null;
      }
    };

    /**
     * 检查是否已有有效的IM登录信息
     */
    const checkExistingIMLogin = async (
      userID: string,
      showMessage: boolean,
    ): Promise<boolean> => {
      const storedUserInfo = localStorage.getItem('TUIKit-userInfo');
      if (!storedUserInfo) return false;

      try {
        const userInfo = JSON.parse(storedUserInfo);
        if (
          new Date(userInfo?.expire) > new Date() &&
          userInfo.userID === userID
        ) {
          // 已有有效登录信息
          if (showMessage && import.meta.env.DEV) {
            ElMessage({
              message: `✅ 用户 ${userID} 已登录，无需重复登录`,
              grouping: true,
              type: 'success',
              duration: 2000,
            });
          }
          return true;
        }
      } catch (error) {
        console.warn('解析存储的用户信息失败:', error);
      }

      return false;
    };

    /**
     * 获取腾讯云IM配置
     */
    const getIMConfig = () => {
      const SDKAppID = Number(import.meta.env.VITE_APP_TENCENT_IM_SDK_APP_ID);
      const secretKey = import.meta.env.VITE_APP_TENCENT_IM_SECRET_KEY;

      if (!SDKAppID || !secretKey) {
        throw new Error('腾讯云IM配置不完整，请检查环境变量配置');
      }

      return { SDKAppID, secretKey };
    };

    /**
     * 设置用户踢出监听
     */
    const setupUserKickoutWatcher = (router: any) => {
      TUIStore.watch(StoreName.USER, {
        kickedOut: (value: string) => {
          if (value && router.currentRoute.value.name !== 'login') {
            localStorage.removeItem('TUIKit-userInfo');
            router.replace({ name: 'login' });
          }
        },
      });
    };

    function $reset() {
      loginLoading.value = false;
      imUserId.value = null;
    }

    return {
      $reset,
      autoIMLogin,
      checkExistingIMLogin,
      getIMConfig,
      getIMUserInfo,
      imUserId,
      loginLoading,
      setupUserKickoutWatcher,
    };
  },
  {
    persist: {
      pick: ['imUserId'],
    },
  },
);
