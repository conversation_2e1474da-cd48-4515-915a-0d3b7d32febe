# 好友列表

## 接口信息

- **请求方法**：POST
- **接口地址**：/im/address/book/getFriendList

#### 请求参数

| 字段 | 类型 | 是否必传 | 描述 |
| --- | --- | --- | --- |
| studentId | string | 否 | 学生id，userType=3时，传学生所对应的身份主键ID编号。 |

#### 请求示例

```json
{
  "studentId": "" //学生id，userType=3时，传学生所对应的身份主键ID编号
}
```

#### 响应参数

| 字段                     | 类型   | 是否必传 | 描述             |
| ------------------------ | ------ | -------- | ---------------- |
| letter                   | string | 是       | 字母             |
| friendList               | array  | 是       | 好友列表         |
| friendList.imUserId      | string | 是       | im编号           |
| friendList.friendRemark  | string | 是       | 备注             |
| friendList.friendName    | string | 是       | 名称             |
| friendList.userIconPath  | string | 是       | 用户头像存放路径 |
| friendList.selfSignature | string | 是       | 个性签名         |

#### 响应示例

```json
[
  {
    "letter": "#",
    "friendList": [
      {
        "imUserId": "",
        "friendRemark": "",
        "friendName": "2anidfef",
        "userIconPath": "",
        "selfSignature": ""
      }
    ]
  },
  {
    "letter": "A",
    "friendList": [
      {
        "imUserId": "",
        "friendRemark": "",
        "friendName": "anidfef",
        "userIconPath": "",
        "selfSignature": ""
      }
    ]
  },
  {
    "letter": "L",
    "friendList": [
      {
        "imUserId": "TEACHER_10008473",
        "friendRemark": "刘汗我加你为好友",
        "friendName": "刘汗",
        "userIconPath": "http://test.amdox.com.cn/static/school_v5/0/0/common_files/21/1658823517000.png",
        "selfSignature": null
      }
    ]
  }
]
```
