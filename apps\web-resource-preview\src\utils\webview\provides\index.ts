import type { WebviewProvide } from '@amdox/webview';

import { isAndroidEnv, isQtEnv, isWindowsEnv } from '@amdox/webview';

import { AndroidProvide } from './android/androidProvide';
import { QtProvide } from './qt/qtProvide';
import { WindowsProvide } from './windows/windowAdapter';

function createWebviewProvide({
  debugPlatforms,
}: {
  debugPlatforms?: string[];
}): undefined | WebviewProvide {
  // Windows
  if (isWindowsEnv() || debugPlatforms?.includes('Windows')) {
    try {
      return new WindowsProvide();
    } catch (error) {
      console.error('Failed to instantiate WindowsAdapter:', error);
    }
  }

  // Android
  else if (isAndroidEnv() || debugPlatforms?.includes('Android')) {
    try {
      return new AndroidProvide();
    } catch (error) {
      console.error(
        'Failed to instantiate AndroidAdapter despite detection:',
        error,
      );
      // Fall through to try Qt or use mock
    }
  }

  // Qt
  else if (isQtEnv() || debugPlatforms?.includes('Qt')) {
    try {
      return new QtProvide();
    } catch (error) {
      console.error(
        'Failed to instantiate QtAdapter despite detection:',
        error,
      );
      // Fall through to use mock
    }
  }
}

export default createWebviewProvide;
