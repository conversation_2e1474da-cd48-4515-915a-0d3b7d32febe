{"name": "@amdox-core/layout-ui", "version": "5.5.5", "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "packages/@amdox-core/uikit/layout-ui"}, "license": "MIT", "type": "module", "scripts": {"build": "pnpm unbuild", "prepublishOnly": "npm run build"}, "files": ["dist"], "sideEffects": ["**/*.css"], "main": "./dist/index.mjs", "module": "./dist/index.mjs", "exports": {".": {"types": "./src/index.ts", "development": "./src/index.ts", "default": "./dist/index.mjs"}}, "publishConfig": {"exports": {".": {"default": "./dist/index.mjs"}}}, "dependencies": {"@amdox-core/composables": "workspace:*", "@amdox-core/icons": "workspace:*", "@amdox-core/shadcn-ui": "workspace:*", "@amdox-core/shared": "workspace:*", "@amdox-core/typings": "workspace:*", "@vueuse/core": "catalog:", "vue": "catalog:"}}