import { onMounted } from 'vue';

const useLoading = (className = '.amdox-loading-container') => {
  let el: any;
  onMounted(() => {
    el = document.querySelector(className);
  });
  function removeLoading() {
    el?.remove();
  }

  function bindEventListener(className: string, eventName: string) {
    const el = document.querySelector(className);
    if (el) {
      el.addEventListener(eventName, removeLoading);
    }
  }
  function removeEventListener(className: string, eventName: string) {
    const audioEl = document.querySelector(className);
    if (audioEl) {
      audioEl.removeEventListener(eventName, removeLoading);
    }
  }
  return {
    el,
    removeLoading,
    bindEventListener,
    removeEventListener,
  };
};

export const useHiddenLoading = (emits: any) => {
  function handleHiddenLoading() {
    emits('loading', false);
  }
  function handleShowLoading() {
    emits('loading', true);
  }
  function bindEventListener(className: string, eventName: string) {
    const el = document.querySelector(className);
    if (el) {
      el.addEventListener(eventName, handleHiddenLoading);
    }
  }
  function removeEventListener(className: string, eventName: string) {
    const audioEl = document.querySelector(className);
    if (audioEl) {
      audioEl.removeEventListener(eventName, handleHiddenLoading);
    }
  }
  return {
    handleHiddenLoading,
    handleShowLoading,
    bindEventListener,
    removeEventListener,
  };
};

export default useLoading;
