# 搜索用户添加

## 接口信息

- **请求方法**: GET
- **接口地址**: `/im/address/book/getImUserListBySearch`

#### 请求参数

| 字段      | 类型   | 是否必传 | 描述                     |
| --------- | ------ | -------- | ------------------------ |
| search    | string | 是       | 搜索手机号码和邮箱       |
| studentId | string | 否       | 学生ID，有值则为学生搜索 |

#### 请求示例

```
GET https://test-im.amdox.com.cn/api/im/address/book/getImUserListBySearch?search=13713933913&studentId=
```

#### 响应参数

| 字段 | 类型 | 是否必传 | 描述 |
| --- | --- | --- | --- |
| imUserId | string | 是 | IM的账号 |
| userName | string | 是 | 操作人 |
| userIconPath | string | 是 | 用户头像存放路径 |
| whetherAdd | number | 是 | 1已添加 2未添加 3不允许任何人添加自己为好友 |

#### 响应示例

```json
[
  {
    "imUserId": "TEACHER_10011917",
    "userName": "ywy",
    "userIconPath": "https://test.amdox.com.cn/static/school_v5/0/0/12/teachingAssistant/data/10011917/other/2025-03-19/801cb87d-a79e-48d5-8d36-4fdfd90fbe88.png",
    "whetherAdd": 2
  },
  {
    "imUserId": "STUDENT_10011062",
    "userName": "你好",
    "userIconPath": "http://picture.amdox.com.cn/static/defaultPhoto.png",
    "whetherAdd": 2
  }
]
```
