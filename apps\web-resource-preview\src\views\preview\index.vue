<script lang="ts" setup>
import type { ChunkInfo } from '#/utils/webview/provides/windows/windowAdapter';

import {
  computed,
  nextTick,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  watch,
} from 'vue';
import { useRoute } from 'vue-router';

import { webviewAdapter } from '@amdox/webview';

import { PPTEventBus } from '#/utils/event-bus';

import Loading from '../../components/loading/Loading.vue';
// 导入预览组件
import AudioPreview from '../../components/preview/audio.vue';
import DocumentPreview from '../../components/preview/document.vue';
import ImagePreview from '../../components/preview/image.vue';
import PdfPreview from '../../components/preview/pdf.vue';
import PresentationPreview from '../../components/preview/presentation.vue';
import SpreadsheetsPreview from '../../components/preview/spreadsheets.vue';
import VideoPreview from '../../components/preview/video.vue';

defineOptions({ name: 'FilePreview' }); // 修改组件名称

interface TestFile {
  name: string;
  url: ArrayBuffer | null | string;
}

interface PreviewState {
  fileName: string;
  isTestUpload: boolean;
}

const isLoading = ref<boolean>(true); // 是否加载中
const route = useRoute();
const fileUrl = ref<ArrayBuffer | string>(''); // 文件地址
const showError = ref(false); // 显示错误
const errorMessage = ref(''); // 错误信息
const showTestList = ref(false); // 新增：控制测试列表显示

const previewState = reactive<PreviewState>({
  fileName: '',
  isTestUpload: false,
});

// 文件扩展名与预览组件的映射
const previewComponents: Record<string, any> = {
  // 文档
  // doc: DocumentPreview,
  docx: DocumentPreview,
  // 表格
  // xls: SpreadsheetsPreview,
  xlsx: SpreadsheetsPreview,
  csv: SpreadsheetsPreview,
  // 演示文稿
  // ppt: PresentationPreview,
  pptx: PresentationPreview,
  // PDF
  pdf: PdfPreview,
  // 图片
  png: ImagePreview,
  jpg: ImagePreview,
  jpeg: ImagePreview,
  gif: ImagePreview,
  bmp: ImagePreview,
  webp: ImagePreview,
  svg: ImagePreview,
  // 音频
  mp3: AudioPreview,
  wav: AudioPreview,
  ogg: AudioPreview,
  m4a: AudioPreview,
  // 视频
  mp4: VideoPreview,
  webm: VideoPreview,
  ogv: VideoPreview,
  // flv: FlvVideoPreview,
  avi: VideoPreview,
  mov: VideoPreview,
  mkv: VideoPreview,
};

// 直接定义测试文件数据
const testFilesData: TestFile[] = [
  {
    name: 'doc(不支持)',
    url: 'https%3A%2F%2Ffilesamples.com%2Fsamples%2Fdocument%2Fdoc%2Fsample1.doc',
  },
  {
    name: 'docx',
    url: 'http%3A%2F%2Ftest.amdox.com.cn%2Fstatic%2Fwhite%2F1%2F10000005%2FcourseWare%2F202005240909001457159.docx',
  },
  {
    name: 'xls(不支持)',
    url: 'https%3A%2F%2Ffilesamples.com%2Fsamples%2Fdocument%2Fxls%2Fsample1.xls',
  },
  {
    name: 'xlsx',
    url: 'http%3A%2F%2Ftest.amdox.com.cn%2Fstatic%2Fwhite%2F1%2F10000005%2FcourseWare%2F202005240909002614697.xlsx',
  },
  {
    name: 'ppt(不支持)',
    url: 'https%3A%2F%2Ffilesamples.com%2Fsamples%2Fdocument%2Fppt%2Fsample1.ppt',
  },
  {
    name: 'pptx',
    url: 'https%3A%2F%2Ftest.amdox.com.cn%2Fstatic%2Fschool_v5%2F0%2F0%2F12%2F%E4%BA%92%E8%81%94%E7%BD%91%E4%BA%A7%E5%93%81%E5%95%86%E4%B8%9A%E8%AE%A1%E5%88%92%E4%B9%A6PPT%E6%A8%A1%E6%9D%BF(www.52ppt.com).pptx',
  },
  {
    name: 'pdf',
    url: 'http%3A%2F%2Ftest.amdox.com.cn%2Fstatic%2Fwhite%2F1%2F10000005%2FcourseWare%2F202005240909001248597.pdf',
  },
  {
    name: 'jpg',
    url: 'https%3A%2F%2Ftest.amdox.com.cn%2Fstatic%2Fschool_v5%2F0%2F0%2F12%2FteachingAssistant%2Fdata%2F11145963%2Ffolder%2F2025-05-20%2F%E5%BE%AE%E4%BF%A1%E6%88%AA%E5%9B%BE_20250520091952.png',
  },
  {
    name: 'jpeg',
    url: 'https%3A%2F%2Ftest.amdox.com.cn%2Fstatic%2Fschool_v5%2F0%2F0%2F12%2FteachingAssistant%2Fdata%2F11145963%2Ffolder%2F2025-05-20%2Fsample_1280%C3%97853.jpeg',
  },
  {
    name: 'png',
    url: 'https%3A%2F%2Ftest.amdox.com.cn%2Fstatic%2Fschool_v5%2F0%2F0%2F12%2FteachingAssistant%2Fdata%2F11145963%2Ffolder%2F2025-05-20%2F%E5%BE%AE%E4%BF%A1%E6%88%AA%E5%9B%BE_20250520091952.png',
  },
  {
    name: 'gif',
    url: 'https%3A%2F%2Ftest.amdox.com.cn%2Fstatic%2Fschool_v5%2F0%2F0%2F12%2FteachingAssistant%2Fdata%2F11145963%2Ffolder%2F2025-05-20%2Fsample_1920%C3%971280.gif',
  },
  {
    name: 'bmp',
    url: 'https%3A%2F%2Ftest.amdox.com.cn%2Fstatic%2Fschool_v5%2F0%2F0%2F12%2FteachingAssistant%2Fdata%2F11145963%2Ffolder%2F2025-05-20%2Fsample_1280%C3%97853.bmp',
  },
  {
    name: 'webp',
    url: 'https%3A%2F%2Ftest.amdox.com.cn%2Fstatic%2Fschool_v5%2F0%2F0%2F12%2FteachingAssistant%2Fdata%2F11145963%2Ffolder%2F2025-05-20%2Fsample1.webp',
  },
  {
    name: 'mp3',
    url: 'https%3A%2F%2Ffilesamples.com%2Fsamples%2Faudio%2Fmp3%2Fsample1.mp3',
  },
  {
    name: 'wav',
    url: 'https%3A%2F%2Ffilesamples.com%2Fsamples%2Faudio%2Fwav%2Fsample3.wav',
  },
  {
    name: 'ogg',
    url: 'https%3A%2F%2Ffilesamples.com%2Fsamples%2Faudio%2Fogg%2Fsample3.ogg',
  },
  {
    name: 'm4a',
    url: 'https%3A%2F%2Ffilesamples.com%2Fsamples%2Faudio%2Fm4a%2Fsample3.m4a',
  },
  {
    name: 'webm',
    url: 'https%3A%2F%2Ffilesamples.com%2Fsamples%2Fvideo%2Fwebm%2Fsample_960x540.webm',
  },
  {
    name: 'ogv',
    url: 'https%3A%2F%2Ffilesamples.com%2Fsamples%2Fvideo%2Fogv%2Fsample_1280x720_surfing_with_audio.ogv',
  },
  {
    name: 'mp4',
    url: 'https%3A%2F%2Ffilesamples.com%2Fsamples%2Fvideo%2Fmp4%2Fsample_960x400_ocean_with_audio.mp4',
  },
  {
    name: 'flv(不支持)',
    url: 'http://example.com/flv/video.flv',
  },
  // {
  //   name: 'flv(不支持)',
  //   url: 'https%3A%2F%2Ffilesamples.com%2Fsamples%2Fvideo%2Fflv%2Fsample_960x400_ocean_with_audio.flv',
  // },
  {
    name: 'avi(不支持)',
    url: 'https%3A%2F%2Ffilesamples.com%2Fsamples%2Fvideo%2Favi%2Fsample_960x400_ocean_with_audio.avi',
  },
  {
    name: 'mov',
    url: 'https%3A%2F%2Ffilesamples.com%2Fsamples%2Fvideo%2Fmov%2Fsample_960x400_ocean_with_audio.mov',
  },
  {
    name: 'mkv',
    url: 'https%3A%2F%2Ffilesamples.com%2Fsamples%2Fvideo%2Fmkv%2Fsample_960x400_ocean_with_audio.mkv',
  },
];

// 使用数组直接初始化 testFiles
const testFiles = ref<TestFile[]>(testFilesData);

// 计算属性，用于获取当前模式 ：test 测试模式  routeParam 路由参数模式 base64模式
const currentModel = computed(() => {
  let mode = 'test';
  if (route.query.file) {
    mode = 'routeParam';
    // console.log('<<< route.query.file >>>', mode);
    return mode;
  }
  if (route.query.mode) {
    mode = route.query.mode as 'base64' | 'routeParam' | 'test';
    // console.log('<<< route.query.mode >>>', mode);
  }

  // console.log('<<< model >>>', mode);
  return mode;
});

// 计算属性，用于获取文件扩展名
const fileExtension = computed(() => {
  const fileName =
    (currentModel.value === 'test' || currentModel.value === 'routeParam') &&
    !previewState.isTestUpload
      ? fileUrl.value
      : previewState.fileName;
  if (typeof fileName !== 'string') return '';
  return fileName.split('.').pop()?.toLocaleLowerCase() || '';
});

// 计算属性，用于获取要渲染的预览组件
const previewComponent = computed(() => {
  const ext = fileExtension.value;
  if (!ext) {
    return null;
  }
  const component = previewComponents[ext];
  return component || null;
});

// 计算属性，用于判断是否显示返回按钮
const isShowBackBtn = computed(() => {
  return currentModel.value === 'test' && !!previewComponent.value;
});
// 计算属性，用于判断是否显示测试列表
const isShowTestList = computed(() => {
  return currentModel.value === 'test' && !previewComponent.value;
});

// 监听当前模型变化
watch(
  currentModel,
  (val) => {
    showError.value = false;
    errorMessage.value = '';
    if (val === 'routeParam') {
      handlePreviewByRoute();
    }
    if (val === 'base64') {
      isLoading.value = true;
      nextTick(() => {
        webviewAdapter.startTransmit?.();
      });
    }
  },
  {
    immediate: true,
  },
);

//  handlePreviewByRoute();
/**
 * @description: 选择预览文件
 * @param {*} url
 * @return {*}
 */
const selectTestFile = (url: string) => {
  fileUrl.value = decodeURIComponent(url);
  showTestList.value = false;
  if (previewComponent.value) {
    showError.value = false;
  } else {
    showError.value = true;
    const ext = fileExtension.value;
    errorMessage.value = ext ? `不支持的文件类型: .${ext}` : '无法确定文件类型';
  }
};

// 创建一个用于处理文件的Web Worker
const fileWorker = new Worker(
  new URL('../../workers/file-worker.ts', import.meta.url),
  { type: 'module' },
);

// 文件分片大小：5MB
const CHUNK_SIZE = 5 * 1024 * 1024;

/**
 * 本地测试 - file预览（使用分片）
 * @param event - 文件上传事件对象
 */
async function handlePreviewUpload(event: Event) {
  // 从事件对象中获取上传的文件
  const file = (event.target as HTMLInputElement).files?.[0];
  if (!file) return;

  // 更新上传状态和文件名

  previewState.fileName = file.name;
  previewState.isTestUpload = true;

  try {
    // 重置界面状态，显示加载提示
    showError.value = false;
    showTestList.value = false;
    errorMessage.value = '文件处理中...';
    showError.value = true;

    // 解析文件类型（image、video、audio）
    const fileType = file.type.split('/')[0];

    // 根据文件类型选择不同的处理方式
    if (['audio', 'image', 'video'].includes(fileType!)) {
      // 媒体文件：使用URL.createObjectURL创建本地预览URL
      const objectUrl = URL.createObjectURL(file);
      fileUrl.value = objectUrl;

      // 组件卸载时清理创建的URL对象，防止内存泄漏
      onUnmounted(() => {
        URL.revokeObjectURL(objectUrl);
      });
    } else {
      // 非媒体文件：使用Web Worker处理
      let handleMessage: ((e: MessageEvent) => void) | null = null;
      let handleError: ((err: ErrorEvent) => void) | null = null;

      // 大文件处理：分片上传（文件大小超过CHUNK_SIZE）
      if (file.size > CHUNK_SIZE) {
        const chunks: ArrayBuffer[] = [];
        let offset = 0;

        // 循环处理每个分片
        while (offset < file.size) {
          // 切割文件为固定大小的分片
          const chunk = file.slice(offset, offset + CHUNK_SIZE);
          // 使用Worker异步处理分片
          const chunkArrayBuffer = await new Promise<ArrayBuffer>(
            (resolve, reject) => {
              // 处理Worker返回的消息
              handleMessage = (e: MessageEvent) => {
                resolve(e.data);
                fileWorker.removeEventListener('message', handleMessage!);
              };

              // 处理Worker错误
              handleError = (err: ErrorEvent) => {
                reject(err);
                fileWorker.removeEventListener('error', handleError!);
              };

              // 添加事件监听并发送分片到Worker
              fileWorker.addEventListener('message', handleMessage);
              fileWorker.addEventListener('error', handleError);
              fileWorker.postMessage({ chunk, type: 'processChunk' });
            },
          );
          // 保存处理后的分片
          chunks.push(chunkArrayBuffer);
          offset += CHUNK_SIZE;
        }

        // 计算所有分片的总长度
        const totalLength = chunks.reduce(
          (acc, chunk) => acc + chunk.byteLength,
          0,
        );
        // 创建合并后的缓冲区
        const mergedBuffer = new ArrayBuffer(totalLength);
        const mergedView = new Uint8Array(mergedBuffer);
        let position = 0;

        // 合并所有分片
        for (const chunk of chunks) {
          mergedView.set(new Uint8Array(chunk), position);
          position += chunk.byteLength;
        }
        fileUrl.value = mergedBuffer;
      } else {
        // 小文件处理：直接发送到Worker处理
        const arrayBuffer = await new Promise<ArrayBuffer>(
          (resolve, reject) => {
            // 处理Worker返回的消息
            handleMessage = (e: MessageEvent) => {
              resolve(e.data);
              fileWorker.removeEventListener('message', handleMessage!);
            };

            // 处理Worker错误
            handleError = (err: ErrorEvent) => {
              reject(err);
              fileWorker.removeEventListener('error', handleError!);
            };

            // 添加事件监听并发送文件到Worker
            fileWorker.addEventListener('message', handleMessage);
            fileWorker.addEventListener('error', handleError);
            fileWorker.postMessage({ file, type: 'processFile' });
          },
        );
        fileUrl.value = arrayBuffer;
      }

      // 清理Worker事件监听器，防止内存泄漏
      if (handleMessage)
        fileWorker.removeEventListener('message', handleMessage);
      if (handleError) fileWorker.removeEventListener('error', handleError);
    }

    // 处理成功，隐藏错误提示
    showError.value = false;
  } catch (error) {
    // 处理失败，显示错误信息
    console.error('读取文件失败:', error);
    showError.value = true;
    errorMessage.value = '读取文件失败，请重试。';
  }
}

/**
 * 本地测试 - base64预览
 * @param event - 文件上传事件对象
 */
async function handleFileUpload(event: Event) {
  const file = (event.target as HTMLInputElement).files?.[0];
  if (!file) return;
  // 更新上传状态和文件名

  previewState.fileName = file.name;
  previewState.isTestUpload = true;
  try {
    const base64 = await fileToBase64(file);
    if (typeof base64 !== 'string') {
      showError.value = true;
      errorMessage.value = '文件处理失败,Base64 数据不是字符串';
      return;
    }
    // 重置界面状态，显示加载提示
    showError.value = false;
    showTestList.value = false;
    errorMessage.value = '文件处理中...';
    showError.value = false;

    // 解析文件类型（image、video、audio）
    const fileType = file.type.split('/')[0];
    // const fileMimeTypeMap: Record<string, any> = {
    //   jpg: 'image/jpeg',
    //   jpeg: 'image/jpeg',
    //   png: 'image/png',
    //   gif: 'image/gif',
    //   bmp: 'image/bmp',
    //   webp: 'image/webp',
    //   mp3: 'audio/mpeg',
    //   wav: 'audio/wav',
    //   ogg: 'audio/ogg',
    //   m4a: 'audio/audio/x-m4a',
    //   webm: 'video/webm',
    //   ogv: 'video/ogg',
    //   mp4: 'video/mp4',
    //   avi: 'video/avi',
    //   mov: 'video/quicktime',
    //   mkv: 'video/x-matroska',
    // };
    // 根据文件类型选择不同的处理方式
    if (['audio', 'image', 'video'].includes(fileType!)) {
      // // 媒体文件：使用URL.createObjectURL创建本地预览URL
      // const objectUrl = URL.createObjectURL(file);
      fileUrl.value = base64;
      // const fileExtension = file.name.split('.').pop()?.toLocaleLowerCase();
      // if (!fileExtension) return;
      // fileUrl.value = `data:${fileMimeTypeMap[fileExtension]};base64,${base64.split(',')[1]}`;

      // 组件卸载时清理创建的URL对象，防止内存泄漏
      // onUnmounted(() => {
      //   URL.revokeObjectURL(objectUrl);
      // });
    } else {
      const newBase64Data = base64.split(',')[1];
      // console.log('<<< newBase64Data >>>', newBase64Data);
      if (!newBase64Data) {
        showError.value = true;
        errorMessage.value = 'Base64 数据为空，无法解析';
        throw new Error('Base64 数据为空，无法解析');
      }
      const binaryString = window.atob(newBase64Data);
      const len = binaryString.length;
      const mergedBuffer = new ArrayBuffer(len);
      const byteArrays = new Uint8Array(mergedBuffer);
      for (let i = 0; i < len; i++) {
        const codePoint = binaryString.codePointAt(i);
        if (codePoint === undefined) {
          showError.value = true;
          errorMessage.value = `Invalid character at index ${i}`;
          throw new Error(`Invalid character at index ${i}`);
        }
        byteArrays[i] = codePoint;
      }
      fileUrl.value = mergedBuffer;
      // console.log('<<< mergedBuffer >>>', mergedBuffer);
    }
  } catch (error: any) {
    showError.value = true;
    errorMessage.value = 'Failed to convert file to base64:';
    console.error('Failed to convert file to base64:', error);
  }
}

/**
 * 将文件转换为Base64
 * @param file
 */
function fileToBase64(file: File) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    const onLoad = () => {
      resolve(reader.result);
      reader.removeEventListener('error', onError);
    };

    const onError = (error: any) => {
      reject(error);
      reader.removeEventListener('load', onLoad);
    };

    reader.addEventListener('load', onLoad);
    reader.addEventListener('error', onError);

    reader.readAsDataURL(file);
  });
}

// /**
//  * @description: 端侧调用预览-blob
//  * @param {*} fileName
//  * @param {*} blod
//  */
// async function handlePreviewByBlob(fileName: string, blod: Blob) {
//   // 更新上传状态和文件名
//   previewState.fileName = fileName;

//   const file = new File([blod], fileName, { type: blod.type });
//   try {
//     // 重置界面状态，显示加载提示
//     showError.value = false;
//     showTestList.value = false;
//     errorMessage.value = '文件处理中...';
//     showError.value = true;

//     // 解析文件类型（image、video、audio）
//     const fileType = file.type.split('/')[0];

//     // 根据文件类型选择不同的处理方式
//     if (['audio', 'image', 'video'].includes(fileType!)) {
//       // 媒体文件：使用URL.createObjectURL创建本地预览URL
//       const objectUrl = URL.createObjectURL(file);
//       fileUrl.value = objectUrl;

//       // 组件卸载时清理创建的URL对象，防止内存泄漏
//       onUnmounted(() => {
//         URL.revokeObjectURL(objectUrl);
//       });
//     } else {
//       // 非媒体文件：使用Web Worker处理
//       let handleMessage: ((e: MessageEvent) => void) | null = null;
//       let handleError: ((err: ErrorEvent) => void) | null = null;

//       // 大文件处理：分片上传（文件大小超过CHUNK_SIZE）
//       if (file.size > CHUNK_SIZE) {
//         const chunks: ArrayBuffer[] = [];
//         let offset = 0;

//         // 循环处理每个分片
//         while (offset < file.size) {
//           // 切割文件为固定大小的分片
//           const chunk = file.slice(offset, offset + CHUNK_SIZE);
//           // 使用Worker异步处理分片
//           const chunkArrayBuffer = await new Promise<ArrayBuffer>(
//             (resolve, reject) => {
//               // 处理Worker返回的消息
//               handleMessage = (e: MessageEvent) => {
//                 resolve(e.data);
//                 fileWorker.removeEventListener('message', handleMessage!);
//               };

//               // 处理Worker错误
//               handleError = (err: ErrorEvent) => {
//                 reject(err);
//                 fileWorker.removeEventListener('error', handleError!);
//               };

//               // 添加事件监听并发送分片到Worker
//               fileWorker.addEventListener('message', handleMessage);
//               fileWorker.addEventListener('error', handleError);
//               fileWorker.postMessage({ chunk, type: 'processChunk' });
//             },
//           );
//           // 保存处理后的分片
//           chunks.push(chunkArrayBuffer);
//           offset += CHUNK_SIZE;
//         }

//         // 计算所有分片的总长度
//         const totalLength = chunks.reduce(
//           (acc, chunk) => acc + chunk.byteLength,
//           0,
//         );
//         // 创建合并后的缓冲区
//         const mergedBuffer = new ArrayBuffer(totalLength);
//         const mergedView = new Uint8Array(mergedBuffer);
//         let position = 0;

//         // 合并所有分片
//         for (const chunk of chunks) {
//           mergedView.set(new Uint8Array(chunk), position);
//           position += chunk.byteLength;
//         }
//         fileUrl.value = mergedBuffer;
//       } else {
//         // 小文件处理：直接发送到Worker处理
//         const arrayBuffer = await new Promise<ArrayBuffer>(
//           (resolve, reject) => {
//             // 处理Worker返回的消息
//             handleMessage = (e: MessageEvent) => {
//               resolve(e.data);
//               fileWorker.removeEventListener('message', handleMessage!);
//             };

//             // 处理Worker错误
//             handleError = (err: ErrorEvent) => {
//               reject(err);
//               fileWorker.removeEventListener('error', handleError!);
//             };

//             // 添加事件监听并发送文件到Worker
//             fileWorker.addEventListener('message', handleMessage);
//             fileWorker.addEventListener('error', handleError);
//             fileWorker.postMessage({ file, type: 'processFile' });
//           },
//         );
//         fileUrl.value = arrayBuffer;
//       }

//       // 清理Worker事件监听器，防止内存泄漏
//       if (handleMessage)
//         fileWorker.removeEventListener('message', handleMessage);
//       if (handleError) fileWorker.removeEventListener('error', handleError);
//     }

//     // 处理成功，隐藏错误提示
//     showError.value = false;
//   } catch (error) {
//     // 处理失败，显示错误信息
//     console.error('读取文件失败:', error);
//     showError.value = true;
//     errorMessage.value = '读取文件失败，请重试。';
//   }
// }

interface FileData {
  chunks: Map<number, string>;
  fileName: string;
  totalChunks: number;
  lastUpdateTime: number;
  isCompleted: boolean;
  mimeType?: string;
}
const fileChunkMap = ref<Map<string, FileData>>(new Map());

/**
 * 端侧调用预览-base64
 * @param chunkInfo
 */
function handlePreviewByBase64(chunkInfo: ChunkInfo) {
  // console.log('<<< chunkInfo >>>', chunkInfo);
  // 获取或创建文件数据对象
  // let fileData = fileChunkMap.value?.get(chunkInfo.fileName);
  let fileData = fileChunkMap.value?.get(chunkInfo.key);
  if (!fileData) {
    fileData = {
      chunks: new Map(),
      fileName: chunkInfo.fileName,
      totalChunks: Number(chunkInfo.totalChunks),
      lastUpdateTime: Date.now(),
      mimeType: chunkInfo.mimeType,
      isCompleted: false,
    } as FileData;
    // fileChunkMap.value?.set(chunkInfo.fileName, fileData);
    fileChunkMap.value?.set(chunkInfo.key, fileData);
  }

  // 更新切片数据
  fileData.chunks?.set(Number(chunkInfo.chunkIndex), chunkInfo.chunkData);
  fileData.lastUpdateTime = Date.now();

  // 检查是否所有切片都已接收完成
  previewState.fileName = chunkInfo.fileName;
  if (fileData.chunks.size === Number(chunkInfo.totalChunks)) {
    fileData.isCompleted = true;
    // mergeAndUploadFile(chunkInfo.fileName);
    mergeAndUploadFile(chunkInfo.key);
  }
}

/**
 * 合并切片
 * @param key 主键
 */
function mergeAndUploadFile(key: string) {
  const fileData = fileChunkMap.value?.get(key);
  if (!fileData) return;
  try {
    const chunks: string[] = [];
    for (let i = 0; i < fileData.totalChunks; i++) {
      const chunk = fileData.chunks.get(i);
      if (!chunk) {
        throw new Error(`缺少切片 ${i}`);
      }
      chunks.push(chunk);
    }
    // 合并base64数据
    const base64Data = chunks.join('');
    // console.log('base64Data', base64Data);
    previewBase64(fileData.fileName, base64Data);
  } catch (error) {
    console.error('合并文件失败:', error);
  }
}

/**
 * @description: 预览base64数据
 * @param {*} fileName
 * @param {*} src
 * @return {*}
 */
function previewBase64(fileName: string, src: any) {
  if (!src || !fileName) return;
  try {
    // // 解析文件类型（image、video、audio）
    const fileExtension = fileName.split('.').pop()?.toLocaleLowerCase();
    if (!fileExtension) return;
    const fileExtensions = [
      'jpg',
      'jpeg',
      'png',
      'gif',
      'bmp',
      'webp',
      'mp3',
      'wav',
      'ogg',
      'm4a',
      'webm',
      'ogv',
      'mp4',
      'avi',
      'mov',
      'mkv',
    ];
    const fileMimeTypeMap: Record<string, any> = {
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      bmp: 'image/bmp',
      webp: 'image/webp',
      mp3: 'audio/mpeg',
      wav: 'audio/wav',
      ogg: 'audio/ogg',
      m4a: 'audio/audio/x-m4a',
      webm: 'video/webm',
      ogv: 'video/ogg',
      mp4: 'video/mp4',
      avi: 'video/avi',
      mov: 'video/quicktime',
      mkv: 'video/x-matroska',
    };
    // 根据文件类型选择不同的处理方式
    if (fileExtensions.includes(fileExtension!)) {
      fileUrl.value = `data:${fileMimeTypeMap[fileExtension]};base64,${src}`;
      return;
    }
    const newBase64Data = src;
    // const newBase64Data = src.split(',')[1];
    if (!newBase64Data) {
      showError.value = true;
      errorMessage.value = 'Base64 数据为空，无法解析';
      throw new Error('Base64 数据为空，无法解析');
    }
    const binaryString = window.atob(newBase64Data);
    const len = binaryString.length;
    const mergedBuffer = new ArrayBuffer(len);
    const byteArrays = new Uint8Array(mergedBuffer);
    for (let i = 0; i < len; i++) {
      const codePoint = binaryString.codePointAt(i);
      if (codePoint === undefined) {
        showError.value = true;
        errorMessage.value = `Invalid character at index ${i}`;
        throw new Error(`Invalid character at index ${i}`);
      }
      byteArrays[i] = codePoint;
    }
    fileUrl.value = mergedBuffer;
  } catch (error: any) {
    isLoading.value = false;
    showError.value = true;
    errorMessage.value = `error: ${error.message}`;
    console.error('Failed to convert file to base64:', error);
  }
}

/**
 * 返回按钮
 */
const goBackToList = () => {
  showTestList.value = true;
  fileUrl.value = '';
  showError.value = false;
  errorMessage.value = '';
  previewState.isTestUpload = false;
  previewState.fileName = '';
};

/**
 * 隐藏加载
 */
function handleHiddenLoading(_isLoading: boolean) {
  isLoading.value = _isLoading;
}

/**
 * 预览文件 - 路由参数方式预览
 */
function handlePreviewByRoute() {
  // 支持资源预览格式： docx、xlsx、pptx、pdf、jpg、jpeg、png、gif、bmp、webp、mp3、wav、ogg、m4a、webm、ogv、mp4、avi、mov、mkv
  // http://172.16.30.78:5777/preview?file=https%3A%2F%2Ftest.amdox.com.cn%2Fstatic%2Fschool_v5%2F0%2F0%2F12%2F%E4%BA%92%E8%81%94%E7%BD%91%E4%BA%A7%E5%93%81%E5%95%86%E4%B8%9A%E8%AE%A1%E5%88%92%E4%B9%A6PPT%E6%A8%A1%E6%9D%BF(www.52ppt.com).pptx
  const fileUrlFromQuery = route.query.file as string; // 路由参数文件
  if (fileUrlFromQuery) {
    fileUrl.value = decodeURIComponent(fileUrlFromQuery);
    if (previewComponent.value) {
      showError.value = false;
    } else {
      showError.value = true;
      const ext = fileExtension.value;
      errorMessage.value = ext
        ? `不支持的文件类型: .${ext}`
        : '无法确定文件类型';
      isLoading.value = false;
    }
  } else {
    showError.value = true;
    errorMessage.value = '没有可用的测试文件，缺少 file URL 参数。';
    isLoading.value = false;
  }
}

onMounted(() => {
  // PPTEventBus.onPreviewByBlob(handlePreviewByBlob);
  PPTEventBus.onPreviewByBase64(handlePreviewByBase64);
});
onUnmounted(() => {
  // PPTEventBus.offPreviewByBlob(handlePreviewByBlob);
  PPTEventBus.offPreviewByBase64(handlePreviewByBase64);
});
</script>

<template>
  <!-- 返回按钮 -->
  <button
    v-if="isShowBackBtn"
    class="back-button"
    style="z-index: 1000"
    @click="goBackToList"
  >
    返回
  </button>
  <!-- 错误提示 -->
  <div v-if="showError" class="error-container">
    <div class="error-message">{{ errorMessage }}</div>
  </div>
  <!--  测试文件列表 -->
  <div v-if="isShowTestList" class="test-file-list">
    <h2>选择一个测试文件进行预览：</h2>
    <ul>
      <li
        v-for="file in testFiles"
        :key="file.name"
        @click="selectTestFile(file.url as string)"
      >
        {{ file.name }}
      </li>
    </ul>
    <div>
      <label>file-worker: </label>
      <input type="file" @change="handlePreviewUpload" />
      <label>base64: </label>
      <input type="file" @change="handleFileUpload" />
      <!-- <button @click="handleUpload">上传</button> -->
    </div>
  </div>
  <!-- 使用动态组件渲染 -->
  <div v-if="previewComponent" class="preview-container">
    <!-- <button
      @click="handleGetPreview"
      class="preview-buttom absolute"
      v-if="isShowBackBtn && fileExtension === 'pptx'"
    >
      预览图片
    </button> -->
    <component
      :is="previewComponent"
      :src="fileUrl"
      @loading="handleHiddenLoading"
      style="width: 100%; height: 100vh"
    />
    <div v-show="isLoading" class="amdox-loading-container">
      <Loading :radius="25" :label-style="() => ({ fontSize: '20px' })" />
    </div>
  </div>
</template>

<style scoped>
.error-container {
  height: 100vh;
}

.error-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 1.5rem;
  color: #f00;
}

.test-file-list {
  padding: 20px;
  text-align: center;
}

.test-file-list h2 {
  margin-bottom: 20px;
}

.test-file-list ul {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
  padding: 0;
  list-style: none;
}

.test-file-list li {
  flex: 0 0 auto;
  padding: 10px 15px;
  cursor: pointer;
  border: 1px solid #ccc;
  border-radius: 5px;
  transition: background-color 0.3s;
}

.test-file-list li:hover {
  background-color: #f0f0f0;
}

.preview-container {
  position: relative;
  width: 100%;
  height: 100vh;

  .amdox-loading-container {
    position: fixed;
    inset: 0;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: rgb(255 255 255);
  }
}

.back-button {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
  padding: 10px 15px;
  color: white;
  cursor: pointer;
  background-color: rgb(0 0 0 / 50%);
  border: none;
  border-radius: 5px;
}

.back-button:hover {
  background-color: rgb(0 0 0 / 70%);
}

.preview-buttom {
  position: absolute;
  top: 20px;
  left: 80px;
  z-index: 10;
  padding: 10px 15px;
  color: white;
  cursor: pointer;
  background-color: rgb(0 0 0 / 50%);
  border: none;
  border: 1px solid rgb(255 255 255);
  border-color: rgb(255 255 255);
  border-radius: 5px;
}
</style>
