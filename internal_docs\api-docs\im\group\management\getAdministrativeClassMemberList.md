# 行政班成员

## 接口信息

- **请求方法**：GET
- **接口地址**：`/im/group/management/getAdministrativeClassMemberList`

#### 请求参数

| 字段    | 类型   | 是否必传 | 描述   |
| ------- | ------ | -------- | ------ |
| clazzId | String | 是       | 班级id |

#### 请求示例

```plaintext
GET https://test-im.amdox.com.cn/im/group/management/getAdministrativeClassMemberList?clazzId=10001438
```

#### 响应参数

| 字段                       | 类型   | 是否必传 | 描述                     |
| -------------------------- | ------ | -------- | ------------------------ |
| studentList                | Array  | 是       | 学生集合                 |
| studentList.imUserId       | String | 是       | 学生IM账号               |
| studentList.userName       | String | 是       | 名称                     |
| studentList.userIconPath   | String | 是       | 用户头像存放路径         |
| teacherList                | Array  | 是       | 教师集合                 |
| teacherList.imUserId       | String | 是       | 教师IM账号               |
| teacherList.userName       | String | 是       | 名称                     |
| teacherList.userIconPath   | String | 是       | 用户头像存放路径         |
| patriarchList              | Array  | 是       | 家长集合                 |
| patriarchList.imUserId     | String | 是       | 家长IM账号               |
| patriarchList.userName     | String | 是       | 名称                     |
| patriarchList.userIconPath | String | 是       | 用户头像存放路径         |
| patriarchList.studentName  | String | 是       | 学生名称，多个用逗号分割 |
| patriarchList.relateName   | String | 是       | 学生关系，多个用逗号分割 |

#### 响应示例

```json
{
  "studentList": [
    {
      "imUserId": "CLASS_S_1_10011722",
      "userName": "张飞",
      "userIconPath": "http://picture.amdox.com.cn/static/defaultPhoto.png"
    }
  ],
  "teacherList": [
    {
      "imUserId": "TEACHER_10013640",
      "userName": "杨明",
      "userIconPath": "http://picture.amdox.com.cn/static/defaultPhoto.png"
    }
  ],
  "patriarchList": [
    {
      "imUserId": "PATRIARCH_11146934",
      "userName": "1",
      "userIconPath": "http://picture.amdox.com.cn/static/defaultPhoto.png",
      "studentName": "张飞",
      "relateName": "1"
    }
  ]
}
```
