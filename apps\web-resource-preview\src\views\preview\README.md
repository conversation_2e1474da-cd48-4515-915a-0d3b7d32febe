# 说明文档

## 预览模式

预览文档分3种模式

1. 测试模式（默认模式）

   访问地址示例：http://172.16.30.78:5777/preview

2. URL模式

   访问地址示例：http://172.16.30.78:5777/preview?file=https%3A%2F%2Ftest.amdox.com.cn%2Fstatic%2Fschool_v5%2F0%2F0%2F12%2F%E4%BA%92%E8%81%94%E7%BD%91%E4%BA%A7%E5%93%81%E5%95%86%E4%B8%9A%E8%AE%A1%E5%88%92%E4%B9%A6PPT%E6%A8%A1%E6%9D%BF(www.52ppt.com).pptx

3. 端侧传输base64数据预览模式

   访问地址示例：http://172.16.30.78:5777/preview?mode=base64

- 端侧传输base64数据预览模式交互说明

  - 端侧需要传输分片base64数据

    - window端侧调用webBrowserObj_ppt_preview_by_base64(data:string)函数传递分片数据 data 参数为JSON字符串,格式如下：
    - 示例：
      ```js
      {
        key: string; // 主键唯一标识符
        fileName: string; // 文件名包含扩展名
        chunkIndex: number; // 分片索引
        chunkData: string; // 分片数据
        totalChunks: number; // 分片总数
      }
      const data =
        "{'key':'xxxxxxx','fileName':'测试.pptx','chunkIndex':1,'chunkData':'xxxxxxxxx','totalChunks':10}";
      ```
      - android端侧调用webview_android_preview_by_base64(json:string)函数传递分片数据 data 参数为JSON字符串,格式如下：
      - 示例：
      ```js
      {
        key: string; // 主键唯一标识符
        fileName: string; // 文件名包含扩展名
        chunkIndex: number; // 分片索引
        chunkData: string; // 分片数据
        totalChunks: number; // 分片总数
      }
      const data =
        "{'key':'xxxxxx','fileName':'测试.pptx','chunkIndex':1,'chunkData':'xxxxxxxxx','totalChunks':10}";
      ```
