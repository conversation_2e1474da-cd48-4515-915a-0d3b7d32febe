# 群成员

## 接口信息

- **请求方法**：GET
- **接口地址**：`/im/member/getMemberList`

#### 请求参数

| 字段    | 类型   | 是否必传 | 描述                        |
| ------- | ------ | -------- | --------------------------- |
| groupId | string | 是       | 群列表的groupId编号（群号） |

#### 请求示例

```plaintext
GET https://test-im.amdox.com.cn/api/im/member/getMemberList?groupId=10000
```

#### 响应参数

| 字段      | 类型   | 是否必传 | 描述                    |
| --------- | ------ | -------- | ----------------------- |
| groupId   | string | 是       | 群组id                  |
| groupName | string | 是       | 群名称                  |
| groupUrl  | string | 是       | 群头像地址              |
| memberNum | number | 是       | 成员数量                |
| groupType | number | 是       | 1班级群 2自定义群 3私聊 |

#### 响应示例

```json
[
  {
    "groupId": "10000",
    "groupName": "一年级一班",
    "groupUrl": "",
    "memberNum": 3,
    "groupType": 3
  }
]
```
