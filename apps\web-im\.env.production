VITE_BASE=/

# 接口地址
VITE_GLOB_API_URL=/api

# 是否开启压缩，可以设置为 none, brotli, gzip
VITE_COMPRESS=none

# 是否开启 PWA
VITE_PWA=false

# vue-router 的模式
VITE_ROUTER_HISTORY=hash

# 是否注入全局loading
VITE_INJECT_APP_LOADING=false

# 打包后是否生成dist.zip
VITE_ARCHIVER=true

# 腾讯 IM 环境
# dev-开发环境 prod-生产环境
VITE_APP_TENCENT_IM_ENV=prod

# 腾讯 IM SDK APP ID【正式环境】
VITE_APP_TENCENT_IM_SDK_APP_ID=1600095998

# 腾讯 IM 密钥【正式环境】
VITE_APP_TENCENT_IM_SECRET_KEY=06342d67aeba364e79243407f576056b17d153fcb8e10384df05e8cc50a9f220
