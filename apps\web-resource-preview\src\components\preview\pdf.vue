<script lang="ts" setup>
// 引入VueOfficePdf组件
import VueOfficePdf from '@vue-office/pdf';

import { useHiddenLoading } from '#/hooks/useLoading';
// 定义组件名称
defineOptions({ name: 'PdfPreview' });

// 定义 props
defineProps<{ src: string }>();

const emits = defineEmits<{
  (e: 'loading'): void;
}>();

const { handleHiddenLoading } = useHiddenLoading(emits);
const renderedHandler = () => {
  // eslint-disable-next-line no-console
  console.log('pdf rendered');
  handleHiddenLoading();
};

const errorHandler = (error: any) => {
  // eslint-disable-next-line no-console
  console.log('pdf error', error);
};
</script>

<template>
  <VueOfficePdf
    :src="src"
    style="height: 100vh"
    @rendered="renderedHandler"
    @error="errorHandler"
  />
</template>
