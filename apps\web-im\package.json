{"name": "@amdox/web-im", "version": "1.0.0", "homepage": "https://test-im.amdox.com.cn", "bugs": "", "repository": {"type": "git", "url": "git+http://*************/web-teaching-magic-cube/teaching-magic-cube-tool.git", "directory": "apps/web-im"}, "license": "private", "author": {"name": "liuzln", "email": "<EMAIL>", "url": "https://github.com/liuzln"}, "type": "module", "scripts": {"build": "pnpm vite build --mode production", "build:analyze": "pnpm vite build --mode analyze", "build:release": "pnpm vite build --mode release", "dev": "pnpm vite --mode development", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "imports": {"#/*": "./src/*"}, "dependencies": {"@amdox-core/icons": "workspace:*", "@amdox-core/shared": "workspace:*", "@amdox/chat-ui": "workspace:*", "@amdox/common-ui": "workspace:*", "@amdox/constants": "workspace:*", "@amdox/hooks": "workspace:*", "@amdox/icons": "workspace:*", "@amdox/locales": "workspace:*", "@amdox/plugins": "workspace:*", "@amdox/preferences": "workspace:*", "@amdox/request": "workspace:*", "@amdox/stores": "workspace:*", "@amdox/styles": "workspace:*", "@amdox/types": "workspace:*", "@amdox/utils": "workspace:*", "@amdox/webview": "workspace:*", "@tencentcloud/call-uikit-vue": "3.3.9", "@tencentcloud/chat-uikit-engine": "latest", "@tencentcloud/chat-uikit-vue": "2.4.3", "@tencentcloud/roomkit-web-vue3": "2.8.2", "@tencentcloud/tui-core": "latest", "@tencentcloud/tui-customer-service-plugin": "latest", "@tencentcloud/tui-emoji-plugin": "latest", "@tencentcloud/universal-api": "latest", "@tiptap/core": "2.0.0-beta.220", "@tiptap/extension-document": "2.0.0-beta.220", "@tiptap/extension-hard-break": "2.0.0-beta.220", "@tiptap/extension-image": "2.0.0-beta.220", "@tiptap/extension-mention": "2.0.0-beta.220", "@tiptap/extension-paragraph": "2.0.0-beta.220", "@tiptap/extension-placeholder": "2.0.0-beta.220", "@tiptap/extension-text": "2.0.0-beta.220", "@tiptap/pm": "2.0.0-beta.220", "@tiptap/suggestion": "2.0.0-beta.220", "@vueuse/core": "catalog:", "axios": "catalog:", "dayjs": "catalog:", "dompurify": "^3.2.1", "element-plus": "catalog:", "highlight.js": "^11.10.0", "marked": "^5.1.2", "marked-highlight": "^2.2.1", "pinia": "catalog:", "qs": "catalog:", "vue": "catalog:", "vue-router": "catalog:"}, "devDependencies": {"@types/lodash": "^4.14.202", "@types/qs": "catalog:", "unplugin-element-plus": "catalog:"}}