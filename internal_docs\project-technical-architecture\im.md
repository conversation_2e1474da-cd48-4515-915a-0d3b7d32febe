## 一、项目整体架构：集中管理的分层模式

我们采用**Monorepo（单仓管理）架构**，就像一个大型工具箱，把所有应用、组件和工具统一存放在一个"仓库"里，方便集中管理和复用。整个架构分为三层：

### 1. 应用层（最上层：直接用的功能）

- **作用**：存放具体的功能应用，就像工具箱里的成品工具。采用**微前端架构**，各应用可以独立开发、部署，同时支持应用间互相调用。
- **包含内容**：
  - **API模拟服务**：开发时模拟后端接口，让前端不用等真实数据就能开发。
  - **资源预览应用**：用来查看教学资源（如文档、图片）的预览功能。
  - **模板应用**：提供通用页面模板，快速搭建新页面。
  - **IM聊天应用**：即时通讯功能，支持群聊、私聊，可调用其他应用功能。
  - **班级管理应用**：班级信息管理、学生管理等教学管理功能。

### 2. 共享包层（中间层：可复用的零件）

- **作用**：存放可重复使用的"零件"，避免重复开发。
- **包含内容**：
  - **核心基础包**：项目的底层框架，比如通用的组件结构。
  - **工具库**：常用功能（如数据处理、网络请求）的封装。
  - **状态管理**：管理应用的数据状态（如用户登录状态）。
  - **国际化**：支持多语言切换的模块。
  - **图标库/样式库**：统一的视觉元素和样式规则。

### 3. 工具链层（底层：开发工具集合）

- **作用**：开发和构建项目的"工具集"，确保开发流程规范高效。
- **包含内容**：
  - **代码规范工具**：自动检查代码格式和质量，像"代码警察"。
  - **构建配置工具**：负责打包项目，让代码能在浏览器/服务器运行。
  - **样式配置工具**：管理样式规则，确保页面美观统一。

## 二、核心技术框架：搭建项目的"积木"

我们用以下技术"积木"搭建项目：

### 1. 微前端架构（应用间协作的"桥梁"）

- **作用**：让不同的应用可以独立开发、独立部署，同时支持应用间互相调用，就像"乐高积木"可以自由组合。
- **应用场景示例**：
  - **IM聊天 → 资源预览**：在聊天中分享文档时，可以直接调用资源预览应用查看文档内容
  - **IM聊天 → 班级管理**：在群聊工具中可以直接打开班级管理功能，查看班级信息
  - **班级管理 → 资源预览**：在班级管理中查看教学资源时，调用资源预览应用
- **技术优势**：
  - **独立性**：每个应用可以独立开发、测试、部署，互不影响
  - **复用性**：一个应用的功能可以被多个其他应用调用
  - **灵活性**：可以根据需要动态加载和卸载应用模块

### 2. 前端开发框架

- **Vue 3**：主流的页面搭建框架，像搭积木一样组合页面组件。
- **TypeScript**：给代码加"类型说明书"，减少出错概率，让开发更安全。
- **Vite**：快速的"项目启动器"，开发时能秒开页面，修改后实时更新。

### 3. UI组件体系（可视化零件库）

我们有一个"组件超市"，包含各种预制的界面元素：

- **表单组件**：登录注册表单、信息录入框等。
- **布局组件**：页面的整体结构（如顶部导航、侧边栏）。
- **弹窗/标签页组件**：常见的交互组件，方便实现弹出提示、多页签切换等功能。

### 4. 样式系统（统一视觉规则）

- **Tailwind CSS**：用预设的"样式类名"快速定义样式，像搭乐高一样组合样式规则。
- **设计令牌系统**：统一管理颜色、字体、间距等视觉变量，确保所有页面风格一致，就像用同一套"设计模板"。

## 三、开发流程：从代码到上线的"生产线"

### 1. 开发流程步骤

![](https://cdn.nlark.com/yuque/__mermaid_v3/e7e1acd28bfb418c1babd1171ca0ef1c.svg)

### 2. 模块化开发优势

- **独立开发**：每个功能模块（如组件、工具库）可以单独开发和测试，就像组装零件一样灵活。
- **自动依赖管理**：系统会自动处理模块间的依赖关系，不用手动调整，避免"牵一发而动全身"。
- **增量更新**：只重新打包修改的部分，节省时间，就像只换坏了的零件，不用重新组装整个工具。

## 四、架构优势：为什么这样设计？

1. **可维护性**：功能模块像抽屉一样分门别类，找问题和改代码更方便。
2. **开发效率**：复用现成的组件和工具，减少重复劳动，就像用预制件盖房子。
3. **一致性**：统一的样式和代码规范，确保所有功能看起来和用起来都很"整齐"。
4. **扩展性**：新增功能时可以直接复用现有组件，像搭积木一样快速扩展。
5. **微前端优势**：
   - **独立部署**：每个应用可以独立发布，不影响其他应用的运行
   - **技术栈灵活**：不同应用可以使用不同的技术栈，适应不同的业务需求
   - **团队协作**：多个团队可以并行开发不同的应用，提高开发效率
   - **按需加载**：用户只加载需要的应用模块，提升页面性能

## 五、总结

教学魔方工具的技术架构就像一个"标准化的工具箱"：

- **上层**是直接服务用户的功能应用；
- **中层**是可复用的"零件仓库"；
- **底层**是开发和维护的"工具集"。

通过这种分层和集中管理模式，我们既能快速开发新功能，又能保证项目稳定、易维护，就像用一套标准化流程生产高质量的工具。
