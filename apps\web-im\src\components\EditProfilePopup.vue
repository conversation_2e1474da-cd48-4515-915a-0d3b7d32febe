<script setup lang="ts">
import BottomPopup from '@amdox/chat-ui/components/common/BottomPopup/index';

const props = withDefaults(
  defineProps<{
    show: boolean;
    title: string;
  }>(),
  {
    show: false,
    title: '',
  },
);
const emits = defineEmits(['onClose', 'onSubmit']);
function onClose() {
  emits('onClose');
}
function onSubmit() {
  emits('onSubmit');
}
</script>
<template>
  <BottomPopup
    class="form-item-bottom-popup"
    :show="props.show"
    :title="props.title"
    :show-header-close-button="true"
    :show-footer-submit-button="true"
    :submit-button-content="$t('确认')"
    border-radius="20px"
    @on-close="onClose"
    @on-submit="onSubmit"
  >
    <slot></slot>
  </BottomPopup>
</template>
<style scoped lang="scss"></style>
