<script setup lang="ts">
import type { IUserProfile } from '@amdox/chat-ui/interface';

import { ref } from 'vue';

import backSVG from '@amdox/chat-ui/assets/icon/back.svg';
import Avatar from '@amdox/chat-ui/components/common/Avatar/index.vue';
import DatePicker from '@amdox/chat-ui/components/common/DatePicker/index.vue';
import Icon from '@amdox/chat-ui/components/common/Icon.vue';
import {
  Toast,
  TOAST_TYPE,
} from '@amdox/chat-ui/components/common/Toast/index';
import { enableSampleTaskStatus } from '@amdox/chat-ui/utils/enableSampleTaskStatus';
import { isH5, isPC } from '@amdox/chat-ui/utils/env';
import { $t } from '@amdox/locales';

import TUIChatEngine, {
  StoreName,
  TUIStore,
  TUIUserService,
} from '@tencentcloud/chat-uikit-engine';
import dayjs, { Dayjs } from 'dayjs';

import calendarSVG from '../assets/icon/calendar.svg';
import rightArrowIcon from '../assets/icon/right-icon.svg';
import EditProfilePopup from './EditProfilePopup.vue';

const emits = defineEmits(['closeEditProfileBox']);
// config
const avatarListBaseUrl =
  'https://im.sdk.qcloud.com/download/tuikit-resource/avatar/avatar_';
const avatarList = ['1.png', '2.png', '3.png', '4.png', '5.png', '6.png'].map(
  (url: string) => avatarListBaseUrl + url,
);
const genderLabelList: {
  [propsName: string]: string;
} = {
  [TUIChatEngine.TYPES.GENDER_MALE]: '男',
  [TUIChatEngine.TYPES.GENDER_FEMALE]: '女',
  [TUIChatEngine.TYPES.GENDER_UNKNOWN]: '不显示',
};
const userProfile = ref<Partial<IUserProfile>>({});
// current edit value
const currentEditProfile = ref<Partial<IUserProfile>>({
  avatar: userProfile.value?.avatar,
  nick: userProfile.value?.nick,
  selfSignature: userProfile.value?.selfSignature,
  gender: userProfile.value?.gender,
  birthday: userProfile.value?.birthday,
});
const birthdayObj = ref<{ format: string; obj: Dayjs | null; value: number }>(
  {} as { format: string; obj: Dayjs | null; value: number },
);
const currentBottomPopupShow = ref<string>('');

TUIStore.watch(StoreName.USER, {
  userProfile: (userProfileData: IUserProfile) => {
    userProfile.value = userProfileData;
    const { avatar, nick, selfSignature, gender, birthday } = userProfileData;
    currentEditProfile.value = {
      avatar,
      nick,
      selfSignature,
      gender,
      birthday,
    };
    birthdayObj.value = generateBirthdayObj(userProfileData.birthday);
  },
});

function generateBirthdayObj(YYYYMMDD: any) {
  let birthdayDayjsObj: Dayjs | null = null;
  let birthdayFormat = '';
  if (YYYYMMDD && typeof YYYYMMDD === 'number') {
    birthdayDayjsObj = dayjs(YYYYMMDD.toString(), 'YYYYMMDD');
    birthdayFormat = birthdayDayjsObj.format('YYYY/MM/DD');
  }
  return {
    obj: birthdayDayjsObj,
    format: birthdayFormat,
    value: userProfile.value?.birthday || 0,
  };
}

function showBottomPopup(key: string) {
  currentBottomPopupShow.value = key;
}
function closeBottomPopup() {
  currentBottomPopupShow.value = '';
}

function pickBirthday(date: Dayjs) {
  currentEditProfile.value.birthday = Number.parseInt(
    date.format('YYYYMMDD'),
    10,
  );
}

function changeCurrentEditProfile(key: keyof IUserProfile, value: any) {
  if (Object.prototype.hasOwnProperty.call(currentEditProfile.value, key)) {
    (currentEditProfile.value as any)[key] = value;
  }
}

function closeEditProfileBox() {
  emits('closeEditProfileBox');
}

function submitEditProfileBox() {
  const isNickModified =
    currentEditProfile.value.nick !== userProfile.value.nick;
  const profileOptions = Object.fromEntries(
    Object.entries(currentEditProfile.value).filter(([_key, value]) => {
      return value !== null && value !== undefined && value !== '';
    }),
  );
  TUIUserService.updateMyProfile(profileOptions)
    .then(() => {
      isNickModified && enableSampleTaskStatus('modifyNickName');
      Toast({
        message: $t('profile.修改个人资料成功'),
        type: TOAST_TYPE.SUCCESS,
      });
      isPC && closeEditProfileBox();
    })
    .catch((error: Error) => {
      Toast({
        message: $t('profile.修改个人资料失败') + error?.message,
        type: TOAST_TYPE.ERROR,
      });
      isPC && closeEditProfileBox();
    });
}
</script>

<template>
  <div
    class="edit-profile-container dialog container"
    :class="[isH5 ? 'edit-profile-container-h5' : 'edit-profile-container-pc']"
    @click="closeEditProfileBox"
    @mousedown.stop
  >
    <div class="edit-profile-box" @click.stop>
      <header class="title">
        <div v-if="isH5" class="title-back" @click="closeEditProfileBox">
          <Icon :file="backSVG" />
        </div>
        <div class="title-name">
          {{ $t('profile.编辑资料') }}
        </div>
      </header>
      <div class="edit-form">
        <div v-if="isH5" class="edit-form-space"></div>
        <div class="edit-form-item">
          <div class="form-label">
            {{ $t('profile.头像') }}
          </div>
          <div v-if="isH5" class="form-info" @click="showBottomPopup('avatar')">
            <Avatar
              use-skeleton-animation
              :url="userProfile.avatar || ''"
              size="60px"
            />
            <Icon class="form-info-arrow" :file="rightArrowIcon" :size="14" />
          </div>
          <EditProfilePopup
            class="form-item"
            :show="isPC || currentBottomPopupShow === 'avatar'"
            :title="$t('profile.选择头像')"
            @on-close="closeBottomPopup"
            @on-submit="submitEditProfileBox"
          >
            <ul class="avatar-list">
              <li
                class="avatar-list-item"
                :class="[
                  currentEditProfile.avatar === avatar &&
                    'avatar-list-item-selected',
                ]"
                v-for="avatar in avatarList"
                :key="avatar"
                @click="changeCurrentEditProfile('avatar', avatar)"
              >
                <Avatar
                  use-skeleton-animation
                  :url="avatar"
                  :size="isPC ? '36px' : '50px'"
                />
              </li>
            </ul>
          </EditProfilePopup>
        </div>
        <div v-if="isH5" class="edit-form-space"></div>
        <div class="edit-form-item">
          <div class="form-label">
            {{ $t('profile.昵称') }}
          </div>
          <div v-if="isH5" class="form-info" @click="showBottomPopup('nick')">
            <div class="form-info-content">{{ userProfile.nick }}</div>
            <Icon class="form-info-arrow" :file="rightArrowIcon" :size="14" />
          </div>
          <EditProfilePopup
            class="form-item"
            :show="isPC || currentBottomPopupShow === 'nick'"
            :title="$t('profile.设置昵称')"
            @on-close="closeBottomPopup"
            @on-submit="submitEditProfileBox"
          >
            <input
              class="form-item-input"
              type="text"
              v-model="currentEditProfile.nick"
            />
          </EditProfilePopup>
        </div>
        <div class="edit-form-item">
          <div class="form-label">
            {{ $t('profile.账号') }}
          </div>
          <div class="form-info">{{ userProfile.userID }}</div>
        </div>
        <div v-if="isH5" class="edit-form-space"></div>
        <div class="edit-form-item">
          <div class="form-label">
            {{ $t('profile.个性签名') }}
          </div>
          <div
            v-if="isH5"
            class="form-info"
            @click="showBottomPopup('selfSignature')"
          >
            <div class="form-info-content">{{ userProfile.selfSignature }}</div>
            <Icon class="form-info-arrow" :file="rightArrowIcon" :size="14" />
          </div>
          <EditProfilePopup
            class="form-item"
            :show="isPC || currentBottomPopupShow === 'selfSignature'"
            :title="$t('profile.个性签名')"
            @on-close="closeBottomPopup"
            @on-submit="submitEditProfileBox"
          >
            <input
              class="form-item-input"
              type="text"
              v-model="currentEditProfile.selfSignature"
            />
          </EditProfilePopup>
        </div>
        <div class="edit-form-item">
          <div class="form-label">
            {{ $t('profile.性别') }}
          </div>
          <div v-if="isH5" class="form-info" @click="showBottomPopup('gender')">
            <div>
              {{
                (userProfile.gender &&
                  $t(`profile.${genderLabelList[userProfile.gender]}`)) ||
                ''
              }}
            </div>
            <Icon class="form-info-arrow" :file="rightArrowIcon" :size="14" />
          </div>
          <EditProfilePopup
            class="form-item"
            :show="isPC || currentBottomPopupShow === 'gender'"
            :title="$t('profile.性别选择')"
            @on-close="closeBottomPopup"
            @on-submit="submitEditProfileBox"
          >
            <ul class="gender-list">
              <li
                class="gender-list-li"
                v-for="(value, key) in genderLabelList"
                :key="key"
                @click="changeCurrentEditProfile('gender', key)"
              >
                <div
                  class="gender-list-item"
                  :class="[
                    currentEditProfile.gender === key &&
                      'gender-list-item-selected',
                  ]"
                  @click="changeCurrentEditProfile('gender', key)"
                >
                  <input
                    v-if="isPC"
                    class="gender-list-radio"
                    type="radio"
                    name="gender"
                    :value="key"
                    :checked="currentEditProfile.gender === key"
                  />
                  {{ $t(`profile.${value}`) }}
                </div>
              </li>
            </ul>
          </EditProfilePopup>
        </div>
        <div class="edit-form-item">
          <div class="form-label">
            {{ $t('profile.出生年月') }}
          </div>
          <div
            v-if="isH5"
            class="form-info"
            @click="showBottomPopup('birthday')"
          >
            <div class="form-info-content">{{ birthdayObj.format }}</div>
            <Icon class="form-info-arrow" :file="rightArrowIcon" :size="14" />
          </div>
          <EditProfilePopup
            class="form-item"
            :show="isPC || currentBottomPopupShow === 'birthday'"
            :title="$t('profile.请选择出生日期')"
            @on-close="closeBottomPopup"
            @on-submit="submitEditProfileBox"
          >
            <div class="birthday-container">
              <DatePicker
                class="birthday-date-picker"
                type="single"
                range-table-type="one"
                :start-placeholder="$t('profile.请选择出生日期')"
                popup-position="top"
                :default-single-date="birthdayObj.obj || undefined"
                @pick="pickBirthday"
              >
                <template #end-icon>
                  <Icon :file="calendarSVG" :size="16" />
                </template>
              </DatePicker>
            </div>
          </EditProfilePopup>
        </div>
      </div>
      <footer v-if="!isH5" class="edit-footer">
        <button class="btn-close" @click="closeEditProfileBox">
          {{ $t('profile.取消') }}
        </button>
        <button class="btn-save" @click="submitEditProfileBox">
          {{ $t('profile.保存') }}
        </button>
      </footer>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@use '../styles/common.scss' as *;

.edit-profile-container {
  /* stylelint-disable-next-line at-rule-no-unknown */
  @extend .container;

  font-size: 14px;

  .edit-profile-box {
    /* stylelint-disable-next-line at-rule-no-unknown */
    @extend .box;

    align-items: stretch;

    .edit-form {
      flex: 1;

      @include flex(column, flex-start, stretch);

      .edit-form-item {
        min-height: 54px;
        padding: 10px;

        @include flex(row, flex-start, center);

        .form-label {
          box-sizing: border-box;
          width: 70px;
          margin-right: 20px;
          color: #333;
        }

        .form-item {
          @include flex(row, flex-start, stretch);

          .avatar-list {
            @include flex(row, space-between, stretch);

            .avatar-list-item {
              margin: 10px;
              border: 1px solid transparent;
            }

            .avatar-list-item:first-child {
              margin-left: 0;
            }

            .avatar-list-item-selected {
              color: #006eff;
              border: 1px solid #006eff;
              border-radius: 5px;
            }
          }

          .form-item-input {
            flex: 1;
            padding: 6px 10px;
            line-height: 20px;
            color: #596174;
            border: 1px solid rgb(131 137 153 / 40%);
            border-radius: 2px;
          }

          .gender-list {
            @include flex(row, space-between, center);

            .gender-list-li {
              margin-right: 20px;

              @include flex(row);

              .gender-list-item {
                flex: 1;
                font-size: 14px;

                @include flex(row);

                .gender-list-item-radio {
                  margin: 0 2px;
                }
              }
            }
          }

          .birthday-container {
            flex: 1;
            padding: 6px 10px;
            border: 1px solid rgb(131 137 153 / 40%);

            @include flex(row, space-between, stretch);

            .birthday-date-picker {
              flex: 1;
              height: 20px;

              @include flex(row);

              :deep(.tui-date-picker-input) {
                flex: 1;

                @include flex(row, flex-start);
              }

              :deep(.tui-date-picker-input-start) {
                padding: 2px 0;
                font-size: 14px;
                text-align: start;

                &::placeholder {
                  text-align: start;
                }
              }

              :deep(.tui-date-picker-dialog-container-one) {
                left: -220px;
              }
            }
          }
        }
      }
    }

    .edit-footer {
      @include flex(row, flex-end);

      .btn-close {
        margin-right: 10px;

        @include btn-normal;
      }

      .btn-save {
        @include btn-default;
      }

      .btn-close,
      .btn-save {
        width: 90px;
        height: 30px;
        padding: 2px;
      }
    }
  }
}

.edit-profile-container-pc {
  .edit-profile-box {
    width: 495px;
    height: 472px;
    padding: 20px;
    border-radius: 10px;

    .edit-form {
      .edit-form-item {
        .form-item {
          flex: 1;
        }
      }
    }
  }

  .title {
    justify-content: flex-start;
    padding: 0;
  }
}

.edit-profile-container-h5 {
  /* stylelint-disable-next-line at-rule-no-unknown */
  @extend .edit-profile-container;

  .edit-profile-box {
    /* stylelint-disable-next-line at-rule-no-unknown */
    @extend .box-h5;

    width: 100%;
    height: 100%;
    background-color: #efefef;

    .title {
      background-color: #fff;
    }

    .edit-form {
      .edit-form-item {
        background-color: #fff;

        .form-label {
          width: 80px;
          font-size: 16px;
          color: #000;
        }

        .form-info {
          flex: 1;
          overflow: hidden;
          font-size: 16px;
          color: rgb(151 151 151 / 100%);

          @include flex(row, flex-end, stretch);

          .form-info-content {
            @include single-line-ellipsis(auto);
          }

          .form-info-arrow {
            margin-left: 2px;
          }
        }

        .form-item {
          flex: none;

          .avatar-list,
          .form-item-input,
          .gender-list,
          .birthday-container {
            margin: 0 20px;
          }

          .avatar-list {
            .avatar-list-item {
              margin: 10px 0;
            }
          }

          .gender-list {
            .gender-list-li {
              margin: 0;

              .gender-list-item {
                width: 120px;
                height: 40px;
                font-size: 16px;
                border: 1px solid rgb(221 221 221 / 100%);
                border-radius: 5px;

                &-selected {
                  color: #006eff;
                  border: 1px solid #006eff;
                  border-radius: 5px;
                }
              }
            }
          }

          .form-item-input {
            padding: 14px 10px;
            font-size: 16px;
            background-color: rgb(248 248 248 / 100%);
            border: 0;
          }
        }
      }

      .edit-form-space {
        box-sizing: border-box;
        height: 10px;
        padding: 0;
        background-color: transparent;
      }
    }
  }
}
</style>
