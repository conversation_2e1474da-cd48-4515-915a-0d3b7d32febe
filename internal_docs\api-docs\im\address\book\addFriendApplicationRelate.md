# 添加好友记录

## 接口信息

- **请求方法**：POST
- **接口地址**：/im/address/book/addFriendApplicationRelate

#### 请求头部

#### 请求参数

| 字段               | 类型   | 是否必传 | 描述                     |
| ------------------ | ------ | -------- | ------------------------ |
| sendImUserId       | string | 是       | 发起IM账号               |
| pickUpImUserId     | string | 是       | 添加者IM账号             |
| sendImName         | string | 是       | 发起者IM名称             |
| sendImUserIconPath | string | 是       | 发起者IM头像地址         |
| remark             | string | 是       | 备注                     |
| remarkName         | string | 是       | 备注名称                 |
| pickUpImState      | number | 是       | 1-待审核 2 已通过 3-忽略 |

#### 请求示例

```json
{
  "sendImUserId": "TEST_TEACHER_10011917",
  "pickUpImUserId": "TEST_TEACHER_11145963",
  "sendImName": "华南F4",
  "sendImUserIconPath": "http://picture.amdox.com.cn/static/defaultPhoto.png",
  "remark": "你好",
  "remarkName": "",
  "pickUpImState": 1
}
```

#### 响应参数

| 字段 | 类型 | 是否必传 | 描述 |
| ---- | ---- | -------- | ---- |

#### 响应示例

```json
null
```

#### 详细说明
