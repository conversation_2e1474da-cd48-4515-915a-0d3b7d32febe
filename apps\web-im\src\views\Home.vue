<script setup lang="ts">
import { ref } from 'vue';

import { <PERSON>Viewer } from '@amdox/chat-ui';
import Drag from '@amdox/chat-ui/components/common/Drag/index.vue';
import { enableSampleTaskStatus } from '@amdox/chat-ui/utils/enableSampleTaskStatus';
import { isH5, isPC } from '@amdox/chat-ui/utils/env';
import { SvgIcon } from '@amdox/icons';
import { useUserStore } from '@amdox/stores';

import { TUICallKit } from '@tencentcloud/call-uikit-vue';
import { StoreName, TUIStore } from '@tencentcloud/chat-uikit-engine';

import {
  addFriendApplicationRelateApi,
  deleteFriendApplicationRelateApi,
  getFriendApplicationPageApi,
  getFriendListApi,
  getImUserListBySearchApi,
  updateFriendApplicationRelateApi,
} from '#/api/im/address';
import { getGroupInfoApi, getGroupList<PERSON><PERSON> } from '#/api/im/group';
import {
  addJoinGroupRelateApi,
  createGroupApi,
  getAdministrativeClassListApi,
  getAdministrativeClassMemberListApi,
  getColonizationMessageRoleListApi,
  getColonizationMessageSchoolListApi,
  getJoinGroupPageApi,
  updateJoinGroupRelateStateApi,
} from '#/api/im/group-management';
import { delGroupMemberApi, getMemberListApi } from '#/api/im/member';
import { addIMUserApi } from '#/api/im/user';
import settingIcon from '#/assets/icon/setting.svg';
import ChatDefaultContent from '#/components/ChatDefaultContent.vue';
import DebugDrawer from '#/components/DebugDrawer.vue';
import { useOrganization } from '#/hooks/useOrganization';
import { useAuthStore } from '#/store/auth';

const authStore = useAuthStore();
const userStore = useUserStore();
const showDebugDrawer = ref<boolean>(false);
const isMenuShow = ref<boolean>(true);
const currentConversationID = ref<string>('');
const isCalling = ref<boolean>(false);
const isMinimized = ref<boolean>(false);
const dragRef = ref<typeof Drag>();
const PCViewerRef = ref<InstanceType<typeof PCViewer>>();

TUIStore.watch(StoreName.CONV, {
  currentConversationID: (id: string) => {
    currentConversationID.value = id;
  },
});

function beforeCalling() {
  isCalling.value = true;
  isMinimized.value = false;
  enableSampleTaskStatus('call');
}

function afterCalling() {
  isCalling.value = false;
  isMinimized.value = false;
}

function onMinimized(
  _oldMinimizedStatus: boolean,
  newMinimizedStatus: boolean,
) {
  isMinimized.value = newMinimizedStatus;
  dragRef?.value?.positionReset();
}

// 切换会话
function handleSwitchConversation(data: any) {
  console.warn('切换会话:', data);
}

// 创建群组
function handleCreateGroup(data: any) {
  console.warn('创建群组:', data);

  // 如果返回的数据包含群组ID，说明群组创建成功
  if (data.groupId) {
    console.warn('群组创建成功，群组ID:', data.groupId);
    console.warn('群组参数:', data.groupParams);
    console.warn('选中的成员:', data.selectedMembers);
  } else {
    // 兼容旧的数据格式，直接返回选中的成员列表
    console.warn('选中的成员:', data);
  }
}
</script>
<template>
  <div class="home" :class="[isH5 && 'home-h5']">
    <div class="home-container" :class="[isMenuShow && 'menu-expand']">
      <div class="home-main">
        <div class="home-TUIKit">
          <!-- 电脑端 -->
          <PCViewer
            v-if="isPC"
            ref="PCViewerRef"
            :add-friend-application-relate-api="addFriendApplicationRelateApi"
            :add-im-user-api="addIMUserApi"
            :add-join-group-relate-api="addJoinGroupRelateApi"
            :create-group-api="createGroupApi"
            :delete-friend-application-relate-api="
              deleteFriendApplicationRelateApi
            "
            :del-group-member-api="delGroupMemberApi"
            :get-administrative-class-list-api="getAdministrativeClassListApi"
            :get-administrative-class-member-list-api="
              getAdministrativeClassMemberListApi
            "
            :get-colonization-message-role-list-api="
              getColonizationMessageRoleListApi
            "
            :get-colonization-message-school-list-api="
              getColonizationMessageSchoolListApi
            "
            :get-friend-application-page-api="getFriendApplicationPageApi"
            :get-friend-list-api="getFriendListApi"
            :get-group-info-api="getGroupInfoApi"
            :get-group-list-api="getGroupListApi"
            :get-im-user-list-by-search-api="getImUserListBySearchApi"
            :get-join-group-page-api="getJoinGroupPageApi"
            :get-member-list-api="getMemberListApi"
            :im-user-id="authStore.imUserId || ''"
            :update-friend-application-relate-api="
              updateFriendApplicationRelateApi
            "
            :update-join-group-relate-state-api="updateJoinGroupRelateStateApi"
            :use-organization="useOrganization"
            :user-info="userStore.userInfo"
            @create-group="handleCreateGroup"
            @switch-conversation="handleSwitchConversation"
          >
            <ChatDefaultContent />
          </PCViewer>
          <!-- 移动端 -->
          <div v-else-if="isH5" class="home-TUIKit-main">
            <div v-if="!currentConversationID" class="home-TUIKit-main">
              <!-- 消息 -->
              <div class="home-TUIKit-main">
                <!-- 搜索全局消息 -->
                <TUISearch search-type="global" />
                <!-- 会话列表 -->
                <TUIConversation />
                <!-- 选择好友 -->
                <TUIContact display-type="selectFriend" />
              </div>
              <!-- 通讯录 -->
              <div class="home-TUIKit-main">
                <TUIContact display-type="contactList" :show-search="false" />
              </div>
            </div>
            <!-- 聊天 -->
            <TUIChat v-else />
            <!-- 群聊管理 -->
            <TUIGroup class="chat-popup" />
            <!-- 搜索会话消息 -->
            <TUISearch class="chat-popup" search-type="conversation" />
          </div>
          <!-- 通话 -->
          <Drag
            ref="dragRef"
            :show="isCalling"
            dom-class-name="callkit-drag-container"
          >
            <TUICallKit
              class="callkit-drag-container"
              :class="[
                `callkit-drag-container-${isMinimized ? 'mini' : isH5 ? 'h5' : 'pc'}`,
              ]"
              :allowed-minimized="true"
              :allowed-full-screen="false"
              :before-calling="beforeCalling"
              :after-calling="afterCalling"
              :on-minimized="onMinimized"
            />
          </Drag>
        </div>
      </div>
    </div>
  </div>
  <DebugDrawer v-if="showDebugDrawer" />
  <div class="debug-toggle" @click="showDebugDrawer = !showDebugDrawer">
    <SvgIcon :file="settingIcon" :size="24" />
  </div>
</template>

<style lang="scss">
@use '../styles/home';
</style>

<style lang="scss" scoped>
.debug-toggle {
  position: fixed;
  right: 30px;
  bottom: 30px;
  z-index: 9998;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  color: white;
  cursor: pointer;
  background-color: #fff;
  border-radius: 50%;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}
</style>
