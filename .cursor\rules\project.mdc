---
description:
globs:
alwaysApply: true
---

# 项目架构文档

## 1. 项目概述

教学魔方工具（teaching-magic-cube-tool）是一个基于现代前端技术栈构建的教育教学平台，旨在提供教学资源管理、课程管理、教师空间、学校空间等功能，为教育教学提供全方位的数字化支持。

## 2. 项目架构

- 基于 TypeScript 开发
- 采用 PNPM 来管理依赖包
  - 使用 PNPM WorkSpace 工作区功能，管理 Monorepo 代码库
  - 使用 PNPM Catalogs 工作区功能，用于定义依赖版本范围作为可重用的常量
- 使用 Turbo 管理和优化 monorepo 的构建过程
- 使用 Vite 来实现代码构建

## 3. 技术栈

### 3.1 核心框架与库

- **前端框架**：Vue 3.5.13
- **构建工具**：Vite 6.3.3
- **路由管理**：Vue Router 4.4.x
- **状态管理**：Pinia 2.2.x + pinia-plugin-persistedstate（持久化）
- **UI组件库**：Element Plus 2.8.x
- **CSS框架**：Tailwind CSS 3.0.x
- **HTTP客户端**：Axios
- **国际化**：Vue I18n 9.13.x

## 4. 项目结构与核心模块

```
- apps/                           Monorepo 项目
  - web-resource-preview/         Web 资源预览
    - src/
      - locale/                   国际化多语言
      - router/                   路由
      - store/                    状态管理
      - components/               组件
      - views/                    页面
      - App.vue                   主组件
      - main.ts                   主文件
  - web-im/                       Web IM 聊天
- internal/                       内部包
  - lint-configs/                 Lint 配置
    - commitlint-config/          Commitlint 配置
    - eslint-config/              ESLint 配置
    - prettier-config/            Prettier 配置
    - stylelint-config/           Stylelint 配置
  - node-utils/                   Node.js 工具库
  - tailwind-config/              TailwindCSS 配置
  - tsconfig/                     TypeScript 配置
  - vite-config/                  Vite 配置
- package/                        共享包
  - @core/                        基础的SDK和UI组件库，请勿将任何业务逻辑和业务包放在该目录。
  - constants/                    常量库
  - types/                        类型库
  - effects/                      副作用库
    - chat-ui/                    腾讯云 IM 聊天 UI 组件库
    - event-bus/                  事件总线
    - websocket-client/           WebSocket 客户端
    - webview/                    WebView 通信库
  - icons/                        图标库
  - locales/                      国际化库
  - preferences/                  偏好设置库
  - stores/                       状态库
    - src/modules/                模块化状态管理
      - access.ts                 权限管理(Token、权限菜单、权限路由)
      - user.ts                   用户状态管理(用户信息)
  - styles/                       样式库
  - types/                        类型库
    - src/
      - im/index.ts               腾讯云 IM 聊天类型定义
      - user.ts                   用户类型定义
  - utils/                        工具库
- scripts/                        脚本
  - turbo-run/                    Turbo 运行脚本
  - vsh/                          管理和维护 monorepo 项目的开发工作流
- turbo.json                      Turbo 配置
- vite.config.ts                  Vite 配置
- pnpm-workspace.yaml             PNPM 项目工作空间依赖管理
```
