<script lang="ts" setup>
import { computed } from 'vue';

const props = withDefaults(
  defineProps<{
    direction?: 'horizontal' | 'vertical';
    iconStyle?: () => { [key: string]: any };
    isShowLabel?: boolean;
    label?: string;
    labelStyle?: () => { [key: string]: any };
    radius?: number;
    ratio?: number;
  }>(),
  {
    ratio: 20, // 圆弧
    label: '加载中，请稍候...',
    isShowLabel: true,
    radius: 8, // 半径
    direction: 'vertical',
    iconStyle: () => ({}),
    labelStyle: () => ({}),
  },
);
const computedLabelStyle = computed(() => props.labelStyle());

const isHorizontal = computed(() => props.direction === 'horizontal');

const iconStyle = computed((): { [key: string]: any } => {
  if (props.direction === 'vertical') {
    return {
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      ...props?.iconStyle,
      ...props?.labelStyle,
    };
  }
  return {};
});

const strokeWidth = computed(() => {
  return props.radius * 2 * 0.18;
});

const d = computed(() => getD(props.ratio));

const getD = (ratio: number) => {
  if (Number.isNaN(ratio) || ratio === 0) return '';
  const r = props.radius - strokeWidth.value / 2;
  const angle = (Math.PI / 50) * ratio;

  const x = r * Math.cos(angle);
  const y = r * Math.sin(angle);
  const isBigAngle = Number(ratio > 50);
  return `M ${r} 0 A ${r} ${r} 0 ${isBigAngle} 1 ${x} ${y}`;
};
</script>
<template>
  <div class="loading-container" :class="{ horizontal: isHorizontal }">
    <div class="loading-icon" :style="iconStyle">
      <svg
        class="svg-loading"
        :width="radius * 2"
        :height="radius * 2"
        :viewBox="`${-radius} ${-radius} ${radius * 2} ${radius * 2}`"
      >
        <circle
          cx="0"
          cy="0"
          :stroke-width="strokeWidth"
          fill="none"
          stroke="#ddd"
          :r="radius - strokeWidth / 2"
        />
        <path
          v-if="ratio > 0"
          :d="d"
          fill="none"
          :stroke-width="strokeWidth"
          stroke="#1486fa"
          stroke-linecap="round"
          class="loading-progress"
        />
      </svg>
    </div>
    <div
      v-if="isShowLabel"
      class="loading-text"
      :class="{ 'loading-text-horizontal': isHorizontal }"
      :style="computedLabelStyle"
    >
      {{ props.label }}
    </div>
  </div>
</template>

<style lang="scss" scoped>
@keyframes ant-rotate {
  to {
    transform: rotate(360deg);
  }
}

.loading-container {
  .loading-icon {
    .svg-loading {
      overflow: visible;
      pointer-events: none;
      transform-origin: 0 0;
    }

    .loading-progress {
      transition:
        stroke-dashoffset 0.6s ease 0s,
        stroke 0.6s ease 0s;
      animation: ant-rotate 1s infinite linear;
    }
  }

  .loading-text {
    margin-top: 16px;
    font-size: 14px;
    color: #888;
    text-align: center;
  }

  .loading-text-horizontal {
    margin-top: 0;
    margin-left: 8px;
  }
}

.horizontal {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
</style>
