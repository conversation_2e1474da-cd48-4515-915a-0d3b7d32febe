{"name": "@amdox/web-resource-preview", "version": "1.0.0", "homepage": "https://test-resource-view.amdox.com.cn", "bugs": "", "repository": {"type": "git", "url": "git+http://*************/web-teaching-magic-cube/teaching-magic-cube-tool.git", "directory": "apps/web-resource-preview"}, "license": "private", "author": {"name": "liuzln", "email": "<EMAIL>", "url": "https://github.com/liuzln"}, "type": "module", "scripts": {"build": "pnpm vite build --mode production", "build:analyze": "pnpm vite build --mode analyze", "dev": "pnpm vite --mode development", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "imports": {"#/*": "./src/*"}, "dependencies": {"@amdox/common-ui": "workspace:*", "@amdox/constants": "workspace:*", "@amdox/event-bus": "workspace:*", "@amdox/hooks": "workspace:*", "@amdox/icons": "workspace:*", "@amdox/locales": "workspace:*", "@amdox/plugins": "workspace:*", "@amdox/preferences": "workspace:*", "@amdox/request": "workspace:*", "@amdox/stores": "workspace:*", "@amdox/styles": "workspace:*", "@amdox/types": "workspace:*", "@amdox/utils": "workspace:*", "@amdox/webview": "workspace:*", "@vue-office/docx": "catalog:", "@vue-office/excel": "catalog:", "@vue-office/pdf": "catalog:", "@vue-office/pptx": "catalog:", "@vueuse/core": "catalog:", "browser-image-compression": "^2.0.2", "dayjs": "catalog:", "docx-preview": "catalog:", "element-plus": "catalog:", "flv.js": "^1.6.2", "html2canvas": "^1.4.1", "mitt": "catalog:", "pinia": "catalog:", "postinstall": "^0.11.1", "vue": "catalog:", "vue-demi": "catalog:", "vue-router": "catalog:"}, "devDependencies": {"unplugin-element-plus": "catalog:"}}