---
# https://vitepress.dev/reference/default-theme-home-page
layout: home
sidebar: false

hero:
  name: Vben Admin
  text: Enterprise-Level Management System Framework
  tagline: Fully Upgraded, Ready to Use, Simple and Efficient
  image:
    src: https://unpkg.com/@amdoxjs/static-source@0.1.7/source/logo-v1.webp
    alt: Vben Admin
  actions:
    - theme: brand
      text: Get Started ->
      link: /en/guide/introduction/vben
    - theme: alt
      text: Live Preview
      link: https://www.vben.pro
    - theme: alt
      text: View on GitHub
      link: https://github.com/vbenjs/vue-vben-admin

features:
  - icon: 🚀
    title: Latest Technology Stack
    details: Based on the latest technology stack, including Vue3, Pinia, Vue Router, TypeScript, etc.
    link: /en/guide/introduction/quick-start
    linkText: Get Started
  - icon: 🦄
    title: Rich Configurations
    details: An enterprise-level frontend solution for middle and back-end systems, offering a wealth of components, templates, and various preference settings.
    link: /en/guide/essentials/settings
    linkText: Configuration Documentation
  - icon: 🎨
    title: Theme Customization
    details: Easily switch between various themes through simple configurations, catering to personalized needs.
    link: /en/guide/in-depth/theme
    linkText: Theme Documentation
  - icon: 🌐
    title: Internationalization
    details: Built-in internationalization support with multiple languages to meet global needs.
    link: /en/guide/in-depth/locale
    linkText: Internationalization Documentation
  - icon: 🔐
    title: Access Control
    details: Built-in access control solutions supporting various permission management methods to meet different access requirements.
    link: /en/guide/in-depth/access
    linkText: Access Documentation
  - title: Vite
    icon:
      src: /logos/vite.svg
    details: Modern frontend build tool with fast cold start and instant hot updates.
    link: https://vitejs.dev/
    linkText: Official Site
  - title: Shadcn UI
    icon:
      src: /logos/shadcn-ui.svg
    details: Core built on Shadcn UI + Tailwindcss, with business support for any UI framework.
    link: https://www.shadcn-vue.com/
    linkText: Official Site
  - title: Turbo Repo
    icon:
      src: /logos/turborepo.svg
    details: Standardized monorepo architecture using pnpm + monorepo + turbo for enterprise-level development standards.
    link: https://turbo.build/
    linkText: Official Site
  - title: Nitro Mock Server
    icon:
      src: /logos/nitro.svg
    details: Built-in Nitro Mock service makes your mock service more powerful.
    link: https://nitro.unjs.io/
    linkText: Official Site
---

<VbenContributors />
