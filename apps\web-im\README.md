# Web IM 即时通讯应用

基于腾讯云 IM 的即时通讯演示应用。

## 开发环境配置

### 环境变量配置

在项目根目录创建 `.env.development` 文件，配置以下环境变量：

```bash
# 腾讯云 IM SDK App ID (必需)
VITE_APP_TENCENT_IM_SDK_APP_ID=your_sdk_app_id

# 腾讯云 IM 密钥 (必需)
VITE_APP_TENCENT_IM_SECRET_KEY=your_secret_key

# 开发模式自动登录用户ID (可选，默认为 linlin)
# 设置为空字符串可禁用自动登录
VITE_MOCK_DATA_IM_AUTO_LOGIN_USER_ID=linlin

# 其他配置
VITE_APP_TITLE=即时通讯演示
VITE_APP_NAMESPACE=amdox-im
VITE_APP_VERSION=1.0.0
VITE_ROUTER_HISTORY=hash
VITE_BASE=/
```

### 自动登录功能

为了方便开发调试，应用支持开发模式下的自动登录功能：

- **仅在开发环境生效**：生产环境下不会执行自动登录
- **可配置用户ID**：通过 `VITE_MOCK_DATA_IM_AUTO_LOGIN_USER_ID` 环境变量配置
- **智能检测**：会检查是否已有有效登录状态，避免重复登录
- **友好提示**：提供详细的状态提示，包括正在登录、登录成功、登录失败等
- **错误处理**：登录失败时会显示详细的错误信息

#### 自动登录逻辑

1. 检查是否为开发环境
2. 获取环境变量中配置的用户ID
3. 检查本地存储是否有有效的登录信息
4. 如果已登录，显示"已登录"提示；如果未登录，执行自动登录
5. 登录过程中显示"正在登录"提示
6. 登录成功后显示"登录成功"提示
7. 如果登录失败，显示详细的错误信息

#### 如何禁用自动登录

如果不需要自动登录功能，可以：

1. 删除或注释 `VITE_MOCK_DATA_IM_AUTO_LOGIN_USER_ID` 环境变量
2. 或者将其设置为空字符串

```bash
# 禁用自动登录
VITE_MOCK_DATA_IM_AUTO_LOGIN_USER_ID=
```

## 使用说明

1. 配置环境变量文件
2. 启动开发服务器：`pnpm dev`
3. 应用会自动使用配置的用户ID登录，并显示相应的状态提示
4. 刷新页面时会智能检测登录状态，无需重复登录，提高开发效率

### 状态提示说明

应用会在不同情况下显示相应的提示信息：

- **🔄 正在自动登录用户: xxx...**：正在执行登录操作
- **🎉 自动登录成功！欢迎回来，xxx**：登录成功
- **✅ 用户 xxx 已登录，无需重复登录**：检测到已有有效登录状态
- **⚠️ 腾讯云IM配置不完整，请检查环境变量配置**：环境变量配置有误
- **❌ 自动登录失败: xxx**：登录失败，显示具体错误信息

## 注意事项

- 自动登录功能仅用于开发环境，请勿在生产环境使用
- 确保腾讯云 IM 的 SDK App ID 和密钥配置正确
- 如果环境变量配置不完整，会跳过自动登录并在控制台显示警告信息
