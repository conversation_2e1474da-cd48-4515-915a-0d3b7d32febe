# 添加事件考勤

## 接口信息

- **请求方法**：POST
- **接口地址**：/cloudClass/clazzManage/addAttendance

#### 请求参数

| 字段         | 类型    | 是否必传 | 描述          |
| ------------ | ------- | -------- | ------------- |
| schoolId     | Number  | Yes      | 学校id        |
| classroomIds | Integer | Yes      | 场地id        |
| clazzList    | Array   | Yes      | 班级id集合    |
| objectName   | String  | Yes      | 年级-班级名称 |

#### 请求示例

{ "schoolId": 10000002, "classroomIds": 10000510, "clazzList": [10000457], "objectName": "高三-1班" }

#### 响应参数

| 字段 | 类型   | 是否必传 | 描述         |
| ---- | ------ | -------- | ------------ |
| data | String | Yes      | 事件考勤UUID |

#### 响应示例

"eaf461a987204b9cb8a5c5e045218ed9"
