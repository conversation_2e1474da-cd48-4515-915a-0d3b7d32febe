@use '../common.scss' as *;

.login {
  flex: 1;
  width: 100%;
  min-width: 980px;
  height: 100%;
  background: #fff;

  @include flex(column, center, stretch);

  .login-main {
    flex: 1;
    padding-bottom: 5vw;

    @include flex(column, center, stretch);

    .login-main-content {
      flex: 1;
      align-self: center;
      width: 100%;
      max-width: 100rem;
      padding: 0 6vh;
      background: url('../assets/image/login-background.png') no-repeat;
      background-position: center left;

      @include flex(row, space-between, center);

      .login-main-adv {
        @include flex(column, flex-start, flex-start);

        .login-main-adv-introduce {
          font-family: PingFangSC-Regular;
          font-size: 3rem;
          font-weight: 400;
          line-height: 4.2rem;
          color: #000;
        }
      }

      .login-sale {
        display: flex;
        align-items: center;
        padding: 8px 16px;
        margin-top: 40px;
        font-size: 1.4rem;
        line-height: 2rem;
        color: #fff;
        cursor: pointer;
        background: url('../assets/image/adv-bg.svg') no-repeat;
        background-position: center;
        background-size: cover;
        border-radius: 6px;

        .icon {
          margin: 0 8px;
        }
      }

      .small-txt {
        width: 42rem;
        font-size: 2.6rem;
        line-height: 3.6rem;
      }

      .checked-text {
        display: flex;
        flex-wrap: wrap;
        font-family: PingFangSC-Regular;
        font-size: 0.88rem;
        font-weight: 400;
        line-height: 1.73rem;
        color: #bbb;
        letter-spacing: 0;

        a {
          color: #006ef0;
        }
      }

      .login-form {
        box-sizing: border-box;
        width: 22.41rem;

        .login-title {
          display: flex;
          font-family: PingFangSC-Medium;
          font-weight: 500;
          color: #000;
          letter-spacing: 0;

          img {
            width: 4.61rem;
            height: 3.23rem;
          }

          p {
            display: flex;
            align-items: center;
            padding-left: 10.5px;
            font-size: 1.8rem;
            line-height: 2.7rem;
          }
        }

        .login-form-item {
          margin: 18px 0;

          .el-select {
            width: 100%;
            padding-top: 28px;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.73rem;
            color: #000;
          }

          .el-input__inner {
            width: 356.4px;
            height: 54px;
            margin-top: 12px;
            background: #fff;
            border: 1.2px solid #ddd;
            border-radius: 4.8px;
          }

          .input-with-select {
            margin-top: 14px;

            input {
              height: 40px;
            }
          }

          .el-input-group__append {
            .code-box {
              color: #006eff;
            }
          }

          .login-form-item-disabled {
            box-sizing: border-box;
            width: 100%;
            padding: 14px 11px;
            margin-top: 28px;
            font-size: 1rem;
            line-height: 1.2rem;
            color: #111;
            letter-spacing: 0;
            cursor: pointer;
            background: #f4f5f9;
            border: 1.2px solid #ddd;
            border-radius: 4.8px;

            label {
              padding-right: 16px;
              color: #999;
              cursor: pointer;
            }
          }
        }

        .login-form-footer {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          text-decoration: none;

          a {
            color: #006ef0;
          }
        }

        .login-btn {
          flex: 1;

          @include flex(column, center, stretch);

          .btn {
            flex: 1;
            width: 100%;
            height: 3rem;
            margin-bottom: 10px;
            font-size: 1.25rem;
            font-weight: 400;
            color: #006eff;
            letter-spacing: 0;
            cursor: pointer;
            background-color: transparent;
            border: 1px solid #006eff;
            border-radius: 5px;

            &:disabled {
              opacity: 0.3;
            }

            &-primary {
              color: #fff;
              background: #006eff;
            }
          }
        }
      }
    }

    .login-main-middle {
      box-sizing: border-box;
      display: flex;
      align-self: center;
      width: 100%;
      max-width: 100rem;
      height: 130px;
      padding: 0 1.6rem 20px;

      .login-main-middle-box {
        display: flex;
        flex: 1;
      }
    }

    .login-main-footer {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      align-self: center;
      width: 100%;
      max-width: 100rem;
      padding: 0 1.6rem;
      background: rgb(231 242 255 / 40%);

      .mask {
        display: flex;
        flex: 0 0 25%;
        flex-direction: column;
        align-items: center;
        padding: 1.6rem 0;

        .mask-top {
          height: 4.19rem;
          font-family: PingFangSC-Regular;
          font-size: 3rem;
          font-weight: 400;
          color: #006eff;
          letter-spacing: 0;
          word-break: break-all;
          white-space: nowrap;
        }

        .mask-under {
          height: 62px;
          font-family: PingFangSC-Regular;
          font-size: 1.2rem;
          font-weight: 400;
          color: #000;
          text-align: center;
          letter-spacing: 0;
          opacity: 0.49;
        }
      }
    }
  }
}
