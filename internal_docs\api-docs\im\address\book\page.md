# 申请记录列表

## 接口信息

- **请求方法**：GET
- **接口地址**：`/im/address/book/page`

#### 请求头部

（无额外参数）

#### 请求参数

##### Query 参数

| 字段       | 类型   | 是否必传 | 描述         |
| ---------- | ------ | -------- | ------------ |
| sendUserId | string | 是       | 申请者IM编号 |
| pageNum    | string | 是       | 页码         |
| pageSize   | string | 是       | 每页数据条数 |

##### Body（raw-json）

无数据

#### 请求示例

```bash
GET /im/address/book/page?sendUserId=TEST_TEACHER_10012455&pageNum=1&pageSize=2
```

#### 响应参数

| 字段               | 类型    | 是否必传 | 描述                               |
| ------------------ | ------- | -------- | ---------------------------------- |
| createBy           | string  | 是       | 操作者id                           |
| createByName       | null    | 是       | 操作人名称                         |
| createTime         | string  | 是       | 创建时间                           |
| updateBy           | string  | 是       | 操作人id                           |
| updateByName       | null    | 是       | 更新人昵称                         |
| updateTime         | string  | 是       | 更新时间                           |
| isDel              | number  | 是       | 是否删除（1-删除，0-使用）         |
| id                 | string  | 是       | 主键                               |
| sendImUserId       | string  | 是       | 发起IM账号                         |
| pickUpImUserId     | string  | 是       | 添加者IM账号                       |
| sendImName         | string  | 是       | 添加者IM名称                       |
| sendImUserIconPath | string  | 是       | 头像路径                           |
| remark             | string  | 是       | 备注                               |
| remarkName         | string  | 是       | 备注名称                           |
| pickUpImState      | number  | 是       | 状态（1-待审核，2-已通过，3-忽略） |
| total              | number  | 是       | 频道内总人数（仅通信场景返回）     |
| size               | number  | 是       | 当前页数量                         |
| current            | number  | 是       | 当前页                             |
| orders             | array   | 是       | 排序字段信息                       |
| optimizeCountSql   | boolean | 是       | 是否优化计数SQL                    |
| searchCount        | boolean | 是       | 是否搜索计数                       |
| maxLimit           | null    | 是       | 最大限制                           |
| countId            | null    | 是       | 计数ID                             |
| pages              | number  | 是       | 总页数                             |

#### 响应示例

```json
{
  "records": [
    {
      "createBy": "10011917",
      "createByName": null,
      "createTime": "2025-01-14 10:00:31",
      "updateBy": "10011917",
      "updateByName": null,
      "updateTime": "2025-01-14 10:02:23",
      "isDel": 0,
      "id": "1",
      "sendImUserId": "T_A_P_10011917",
      "pickUpImUserId": "CLASS_S_1_10011071",
      "sendImName": "sass",
      "sendImUserIconPath": "http://picture.amdox.com.cn/static/defaultPhoto.png",
      "remark": "你好",
      "remarkName": "张三",
      "pickUpImState": 2
    }
  ],
  "total": 1,
  "size": 2,
  "current": 1,
  "orders": [],
  "optimizeCountSql": true,
  "searchCount": true,
  "maxLimit": null,
  "countId": null,
  "pages": 1
}
```
