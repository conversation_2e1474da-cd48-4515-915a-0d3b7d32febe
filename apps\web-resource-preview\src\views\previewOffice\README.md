# office356 提供的参考文档

## 调用

http(s)://ow365.cn/?i=您的网站ID&furl=要预览的Office文件下载地址

## 参数

- i:注册用户后添加域名自动生成的值。
- furl:公网上服务器存放office的文件地址。
- ssl=1:如果文件地址是以https开头，则需要添加参数ssl=1。
- fname:重命名显示的文件名，不需要加扩展名。
- n=1:word文档会在PC端强制运行移动端的精简模式。
- n=3:word文档在PC端，移动端均采用PC端的预览样式同时隐藏PPT底部操作栏。
- n=5:ppt采用精确模式预览，支持gif图。
- n=6:PDF 预览默认是无法复制的，如果想PDF 可复制，请加参数n=6。但清晰度会所降低。
- n=7:PDF文档高清预览。
- n=5&htool=1:隐藏PPT底部操作栏。
- info=0:获取文档信息，返回的为json格式数据，包括：文件名、末次修改时间、页数、文档梗概等。
- info=0&words=字数：返回word、PDF中正文内容的字数words为0或省略，则返回全部文字。
- info=1:获取word,excel.ppt,pdf首页缩略图。
- info=1&words=页数：获取文档指定页数的原图，返回格式为zip的压缩包。
- info=2:将word, ppt,pdf等文档转换为pdf文件并直接下载。
- info=3:将word,excel.ppt,pdf等文档转换为pdf预览，并提供下载打印按钮。此参数不支持IE浏览器。
- info=4:Word、Excel、PDF、PPT 会进入不提供下载只能打印的界面。此参数不支持
- IE9 及以下的浏览器。该模式相比较info=3，在格式精准度上（包括特殊字体）没有原生的PDF 文件精确，但一般文档均没有问题。
- info=5:高级版，返回所有PPT 页面的备注文字，返回的为json格式数据。
- info=6:返回文件中图片链接。

> 注：所有的info 参数仅支持高级版及以上的版本使用。

# web包装后的文档

## 调用

https://test-resource-view.amdox.com.cn/#/preview_office?furl="文件地址"

## 交互

### 端侧API（web调用端侧）

1. 预览office-获取PPT信息  
   getOfficePageInfoRes(json:string):void

```
getOfficePageInfoRes(json:string);

字符串JSON：
            {
                "info": "ow365",
                "anim": {   // 动画信息
                    "total": "3",  // 动画总帧数
                    "idx": "0" // 当前帧
                },
                "page": {
                    "total": "46", // 总页数
                    "idx": "1" // 当前页
                },
                "url": "xxxxx", // 当前页面链接
                "load": "200",
                "hrefVal": "",
                "ev": "init",
                "previewInfo": {
                    "fileName": "2、《安道教学工具》产品介绍.pptx", // 文件名
                    "pageCount": 46, // 页数
                    "list": [ // 缩略图数组
                        "xxxxxxx",
                        "xxxxxx"
                    ]
                }
            }

```

### web端API（端侧调用WEB）

各端调用用，前缀加函数名

```
示例：
    webview_android_ppt_next_page
```

各端前缀

- 安卓  
  webview_android
- windows  
  webBrowserObj
- ios  
  webview_qt

1. 翻下一页  
   ppt_next_page(void):void
1. 翻上一页  
   ppt_prev_page(void):void
1. 上一帧动画  
   ppt_prev_anim(void):void
1. 下一帧动画  
   ppt_next_anim(void):void
1. 跳转指定页面  
   ppt_jump_to_page(pageIndex:number):void
