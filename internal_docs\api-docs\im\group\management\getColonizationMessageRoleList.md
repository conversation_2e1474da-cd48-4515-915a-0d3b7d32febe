# 岗位与成员列表

## 接口信息

- **请求方法**：GET
- **接口地址**：`/im/group/management/getColonizationMessageRoleList?campusId=1000095249`

#### 请求参数

| 字段 | 类型 | 是否必传 | 描述 |
| --- | --- | --- | --- |
| campusId | string | 否 | 校区ID（不传则返回请求头部的学校下的所有岗位与成员列表） |

#### 请求示例

```json
{
  "campusId": "1000095249"
}
```

#### 响应参数

| 字段                 | 类型    | 是否必传 | 描述                    |
| -------------------- | ------- | -------- | ----------------------- |
| schoolId             | string  | 是       | 学校ID                  |
| roleId               | string  | 是       | 角色ID                  |
| roleName             | string  | 是       | 角色名称                |
| dtoList              | array   | 是       | 用户信息列表            |
| dtoList.imUserId     | string  | 是       | IM用户编号              |
| dtoList.userName     | string  | 是       | 用户名称                |
| dtoList.userIconPath | string  | 是       | 用户头像存放路径        |
| dtoList.whetherAdd   | Null    | 否       | 在此用不上              |
| dtoList.isFriend     | boolean | 是       | true: 好友, false: 不是 |

#### 响应示例

```json
[
  {
    "schoolId": "10000002",
    "roleId": "282",
    "roleName": "学校管理员",
    "dtoList": [
      {
        "imUserId": "TEACHER_11159579",
        "userName": "程惠",
        "userIconPath": "http://picture.amdox.com.cn/static/defaultPhoto.png",
        "whetherAdd": null,
        "isFriend": true
      }
    ]
  }
]
```
