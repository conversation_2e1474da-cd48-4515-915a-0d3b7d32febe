import { defineConfig } from '@amdox/vite-config';

import ElementPlus from 'unplugin-element-plus/vite';

export default defineConfig(async () => {
  return {
    application: {},
    vite: {
      plugins: [
        ElementPlus({
          format: 'esm',
        }),
      ],
      server: {
        proxy: {
          '/api': {
            changeOrigin: true,
            rewrite: (path) => path.replace('/api', ''),
            // mock代理目标地址
            target: 'https://test-web-im.amdox.com.cn/api',
            ws: true,
          },
        },
      },
    },
  };
});
