import type { SupportedLanguagesType } from '@amdox/types';
import type { UsbItem, WebviewProvide } from '@amdox/webview';

import { loadLocaleMessages } from '@amdox/locales';
import { preferences } from '@amdox/preferences';

export class WindowsProvide implements WebviewProvide {
  public app_install_success: (data: string) => Promise<void>;
  public update_download_progress: (data: string) => Promise<void>;
  public update_language: (locale: string) => Promise<void>;
  public updateUsb: (usbData: UsbItem[]) => Promise<void>;

  constructor() {
    /**
     * 应用安装成功
     * @param data
     * @returns
     */
    this.app_install_success = (_data: string): Promise<void> => {
      // eslint-disable-next-line no-console
      console.log('[WindowsProvide] app_install_success called with:', _data);
      return Promise.resolve();
    };

    /**
     * 更新下载进度
     * @param _data
     * @returns
     */
    this.update_download_progress = (_data: string): Promise<void> => {
      // eslint-disable-next-line no-console
      console.log(
        '[WindowsProvide] update_download_progress called with:',
        _data,
      );
      return Promise.resolve();
    };

    /**
     * 更新语言
     * @param locale
     * @returns
     */
    this.update_language = async (locale: string): Promise<void> => {
      // 转换为支持的语言类型
      let lang: SupportedLanguagesType;
      switch (locale) {
        case 'en-US': {
          lang = 'en-US';
          break;
        }
        case 'es-ES': {
          lang = 'es-ES';
          break;
        }
        case 'hi': {
          lang = 'hi-IN'; // 假设 'hi' 对应 'hi-IN'
          break;
        }
        case 'ru': {
          lang = 'ru-RU'; // 假设 'ru' 对应 'ru-RU'
          break;
        }
        case 'zh-CN': {
          lang = 'zh-CN';
          break;
        }
        case 'zh-TW': {
          // 添加繁体中文支持
          lang = 'zh-TW';
          break;
        }
        default: {
          // 未匹配则不作转换，使用传入的 locale
          lang = locale as SupportedLanguagesType;
        }
      }
      // eslint-disable-next-line no-console
      console.log('update_language locale', locale, 'lang', lang);
      await loadLocaleMessages(lang);
      preferences.app.locale = lang;
    };

    /**
     * 更新 USB
     * @param usbData
     * @returns
     */
    this.updateUsb = (_usbData: UsbItem[]): Promise<void> => {
      // eslint-disable-next-line no-console
      console.log('[WindowsProvide] updateUsb called with:', _usbData);
      return Promise.resolve();
    };
  }
}
