import * as CryptoJS from 'crypto-js';

// 秘钥，必须由16位字符组成
const secretKey = '122FF23419AB14EF'; // 不用乱改（修改需要一后端一起改）
// 16位十六进制数作为密钥
const key = CryptoJS.enc.Utf8.parse('122FF23419AB14EF');
// 16位十六进制数作为密钥偏移量
const iv = CryptoJS.enc.Utf8.parse('1234567890abcdef');

/**
 * @description: 加密内容(项目内部使用)
 * @param {*} encryptText  明文
 * @return {*} 密文
 */
function encrypt(encryptText: string, secretIv?: string) {
  const text = CryptoJS.enc.Utf8.parse(encryptText);
  const _iv = secretIv === undefined ? iv : CryptoJS.enc.Utf8.parse(secretIv);

  const encrypted = CryptoJS.AES.encrypt(text, key, {
    iv: _iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  return encrypted.toString();
}

/**
 * @description: 解密内容
 * @param {*} decryptText  密文
 * @return {*} 明文
 */
function decrypt(decryptText: string) {
  const decrypted = CryptoJS.AES.decrypt(decryptText, key, {
    iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  const decryptedStr = decrypted.toString(CryptoJS.enc.Utf8);
  return decryptedStr.toString();
}

/**
 * @description: 解密内容
 * @param {*} decryptText  密文
 * @return {*} 明文
 */
function decryptBySecretIV(decryptText: string, secretIV: string) {
  const cfg = {
    iv: CryptoJS.enc.Utf8.parse(secretIV),
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  };
  const decrypted = CryptoJS.AES.decrypt(decryptText, key, cfg);
  const decryptedStr = decrypted.toString(CryptoJS.enc.Utf8);
  return decryptedStr.toString();
}

/**
 * @description: AES加密方法
 * @param content 要加密的字符串
 * @returns {string} 加密结果
 */
function aesEncrypt(content: string) {
  const key = CryptoJS.enc.Utf8.parse(secretKey);
  const srcs = CryptoJS.enc.Utf8.parse(content);
  const encrypted = CryptoJS.AES.encrypt(srcs, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  });
  return encrypted.toString();
}

/**
 * @description:AES解密方法
 * @param encryptStr 密文
 * @returns {string} 明文
 */
function aesDecrypt(encryptStr: string) {
  const key = CryptoJS.enc.Utf8.parse(secretKey);
  const decrypt = CryptoJS.AES.decrypt(encryptStr, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  });
  return CryptoJS.enc.Utf8.stringify(decrypt).toString();
}

/**
 * @description: MD5加密
 * @param {*} content
 * @return {*}
 */
function MD5(content: string) {
  return CryptoJS.MD5(content);
}

/**
 * @description: 加密成Base64
 * @param {*} encStr 加密字符串
 * @return {*} 密文
 */
function encBase64(encStr: string) {
  return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(encStr));
}

/**
 * @description: 解密Base64内容
 * @param {*} decStr 解密字符串
 * @return {*} 明文
 */
function decBase64(decStr: string) {
  return CryptoJS.enc.Base64.parse(decStr).toString(CryptoJS.enc.Utf8);
}

/**
 * @description: 解密JWT内容
 * @param {*} token 解密字符串
 * @return {*} 明文
 */
function decodeJwt(token: string) {
  // Split the token into its three parts.
  const parts = token.split('.');

  // Decode the header and payload.
  const header = JSON.parse(
    atob(parts[0]?.replaceAll('-', '+').replaceAll('_', '/') ?? ''),
  );
  const payload = JSON.parse(
    atob(parts[1]?.replaceAll('-', '+').replaceAll('_', '/') ?? ''),
  );

  return { header, payload };
}

/**
 * @description: 生成随机的盐值
 * @param {number} length 长度
 * @return {string} 随机盐值
 */
function generateSalt(length = 128) {
  return CryptoJS.lib.WordArray.random(length / 8).toString(CryptoJS.enc.Hex);
}

export {
  aesDecrypt,
  aesEncrypt,
  decBase64,
  decodeJwt,
  decrypt,
  decryptBySecretIV,
  encBase64,
  encrypt,
  generateSalt,
  MD5,
};
