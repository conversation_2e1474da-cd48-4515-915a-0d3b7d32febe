# 分页-获取学生考勤记录

## 接口信息

- **请求方法**：GET
- **接口地址**：/cloudClass/clazzManage/getAttendanceRecords/{页码}/{每页数量}

#### 请求参数

| 字段      | 类型    | 是否必传 | 描述   |
| --------- | ------- | -------- | ------ |
| studentId | Integer | 是       | 学生id |

#### 请求示例

`/cloudClass/clazzManage/getAttendanceRecords/1/15?studentId=10010833`

#### 响应参数

| 字段                   | 类型 | 是否必传 | 描述 |
| ---------------------- | ---- | -------- | ---- |
| (根据实际返回字段补充) |      |          |      |

#### 响应示例

{ "data": { "records": [ { "attendanceTime": "2022-07-18 17:33:06", "status": "Completed" } ], "total": 15 } }
