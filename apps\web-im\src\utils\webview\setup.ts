import type { SupportedLanguagesType } from '@amdox/locales';
import type { WebviewProvide } from '@amdox/webview';

import { loadLocaleMessages } from '@amdox/locales';
import { preferences } from '@amdox/preferences';
import { useAccessStore, useUserStore } from '@amdox/stores';
import {
  getTokens,
  isAndroidEnv,
  isQtEnv,
  isWebviewEnv,
  isWindowsEnv,
  setupWebviewProvideMethods,
  setZoom,
  webviewAdapter,
} from '@amdox/webview';

import createWebviewProvide from './provides';

// 初始化 webview 环境
export const setupWebviewEnv = async () => {
  if (isWebviewEnv()) {
    const accessStore = useAccessStore();
    const userStore = useUserStore();

    // -- 获取 Token --
    const { newToken, oldToken } = await getTokens();
    // eslint-disable-next-line no-console
    console.log('<<< newToken >>>', newToken);
    // eslint-disable-next-line no-console
    console.log('<<< oldToken >>>', oldToken);

    if (newToken) {
      accessStore.setAccessToken(newToken);
    }
    if (oldToken) {
      accessStore.setOldAccessToken(oldToken);
    }

    // -- 获取用户信息 --
    try {
      if (webviewAdapter.getUserInfo) {
        const res = await webviewAdapter.getUserInfo();
        const userInfoObj = JSON.parse(res);
        // eslint-disable-next-line no-console
        console.log('<<< userInfoObj >>>', userInfoObj);
        if (Object.keys(userInfoObj).includes('schoolId')) {
          userStore.setUserInfo(userInfoObj);
        } else {
          const schoolId =
            userInfoObj.schoolDtoList.length > 0
              ? userInfoObj.schoolDtoList[0].schoolId
              : '';
          userStore.setUserInfo({ ...userInfoObj, schoolId });
        }
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }

    // Windows 环境
    if (isWindowsEnv()) {
      // 平台特定逻辑
      // 获取 webview 数据
      try {
        // 获取语言
        if (webviewAdapter.getLanguage) {
          const locale = await webviewAdapter.getLanguage();
          // eslint-disable-next-line no-console
          console.log('<<< locale >>>', locale);
          await loadLocaleMessages(locale);
          preferences.app.locale = locale as SupportedLanguagesType;
        }
      } catch (error) {
        console.error('error', error);
      }
    }
    // QT 环境（Kylin UOS）
    else if (isQtEnv()) {
      // 平台特定逻辑
    }
    // Android 环境
    else if (isAndroidEnv()) {
      setZoom();
    }

    const webviewProvide = createWebviewProvide() as WebviewProvide;
    // 设置 Webview 主动调用的方法
    setupWebviewProvideMethods(webviewProvide);
  }
};
