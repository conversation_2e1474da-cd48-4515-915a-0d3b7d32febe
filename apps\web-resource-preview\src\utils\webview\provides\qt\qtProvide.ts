import type { WebviewProvide } from '@amdox/webview';

import { PPTEventBus } from '#/utils/event-bus';

export class QtProvide implements WebviewProvide {
  /**
   * 获取分页信息
   */
  public ppt_get_page_info = (): Promise<{
    pageIndex: number;
    totalCount: number;
  }> => {
    // eslint-disable-next-line no-console
    console.log('getPageInfo');
    // 请求获取页面信息
    return PPTEventBus.requestPageInfo();
  };

  /**
   * 下一页
   */
  public ppt_next_page = (): Promise<void> => {
    // eslint-disable-next-line no-console
    console.log('nextPage');
    // 发送下一页事件
    PPTEventBus.emitNextPage();
    return Promise.resolve();
  };

  /**
   * 上一页
   */
  public ppt_prev_page = (): Promise<void> => {
    // eslint-disable-next-line no-console
    console.log('prevPage');
    // 发送上一页事件
    PPTEventBus.emitPrevPage();
    return Promise.resolve();
  };

  /**
   * 设置当前页码
   */
  public ppt_set_current_page = (pageIndex: number): Promise<void> => {
    // eslint-disable-next-line no-console
    console.log('setCurrentPage', pageIndex);
    // 发送跳转到指定页事件
    PPTEventBus.emitGoToPage(pageIndex);
    return Promise.resolve();
  };
}
