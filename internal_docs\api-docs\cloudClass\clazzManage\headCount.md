# 照片统计人数

## 接口信息

- **请求方法**：POST
- **接口地址**：/cloudClass/clazzManage/headCount

#### 请求参数

| 字段       | 类型   | 是否必传 | 描述                     |
| ---------- | ------ | -------- | ------------------------ |
| imageBas64 | String | 是       | 照片生成的base64文件内容 |

#### 请求示例

```json
{
  "imageBas64": "iVBORw0KGgoAAAANSUhEUgAAAksAAAFHCAYAAACxlwjk..."
}
```

#### 响应参数

| 字段            | 类型    | 是否必传 | 描述               |
| --------------- | ------- | -------- | ------------------ |
| detectionMsg    | String  | 是       | 检测结果           |
| headCount       | Integer | 是       | 识别到的人脸个数   |
| ImageHeight     | Number  | 是       | 图片高度           |
| ImageWidth      | Number  | 是       | 图片宽度           |
| FaceInfos       | Array   | 是       | 人脸坐标及属性信息 |
| FaceQualityInfo | Object  | 是       | 人脸质量评分       |
| X               | Number  | 是       | 人脸坐标X          |
| Y               | Number  | 是       | 人脸坐标Y          |
| Height          | Number  | 是       | 人脸高度           |
| Width           | Number  | 是       | 人脸宽度           |
| Beauty          | Number  | 是       | 颜值评分           |
| Gender          | Number  | 是       | 性别标识           |
| Age             | Number  | 是       | 年龄预测           |

#### 响应示例

```json
{
  "detectionMsg": "检测完成,如人脸有遮挡，结果未必准确！",
  "headCount": 8,
  "ImageHeight": 1080,
  "ImageWidth": 1920,
  "FaceInfos": [
    {
      "X": 253,
      "Y": 744,
      "Height": 72,
      "Width": 79,
      "Beauty": 75,
      "Gender": 99,
      "Age": 31
    },
    {
      "X": 1298,
      "Y": 791,
      "Height": 76,
      "Width": 68,
      "Beauty": 78,
      "Gender": 99,
      "Age": 25
    }
  ]
}
```
