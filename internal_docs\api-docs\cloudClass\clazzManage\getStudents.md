# 获取班级所有学生信息

## 接口信息

- **请求方法**：GET
- **接口地址**：/cloudClass/clazzManage/getStudents

#### 请求参数

| 字段      | 类型    | 是否必传 | 描述         |
| --------- | ------- | -------- | ------------ |
| checkUuid | String  | Yes      | 事件考勤uuid |
| schoolId  | Integer | Yes      | 学校id       |
| clazzId   | Integer | Yes      | 班级id       |

#### 响应参数

| 字段                         | 类型    | 是否必传 | 描述       |
| ---------------------------- | ------- | -------- | ---------- |
| latestAttendance.uuid        | String  | Yes      | 主键id     |
| latestAttendance.schoolId    | Integer | Yes      | 学校id     |
| latestAttendance.classroomId | Integer | Yes      | 场地id     |
| signIn                       | Integer | Yes      | 签到人数   |
| studentList[].studentId      | Integer | Yes      | 学生id     |
| studentList[].studentName    | String  | Yes      | 学生姓名   |
| studentList[].studentNo      | String  | Yes      | 学号       |
| studentList[].gender         | String  | Yes      | 性别       |
| studentList[].checkStatus    | String  | Yes      | 签卡状态   |
| unSignIn                     | Integer | Yes      | 未签到人数 |

#### 响应示例

{ "latestAttendance": { "uuid": "", "schoolId": 0, "classroomId": 0 }, "signIn": 0, "studentList": [ { "studentId": 10010833, "studentName": "一月", "studentNo": "2021090601", "gender": "男", "checkStatus": "未打卡" } ], "unSignIn": 104 }
