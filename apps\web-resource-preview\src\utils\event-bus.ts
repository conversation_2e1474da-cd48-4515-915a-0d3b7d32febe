import type { ChunkInfo } from './webview/provides/windows/windowAdapter';

import { createEventBus } from '@amdox/event-bus';

/**
 * 项目专用的事件总线实例
 */
export const eventBus = createEventBus();

/**
 * PPT 预览相关的事件工具类
 * 基于通用事件总线实现具体的业务逻辑
 */
export const PPTEventBus = {
  /**
   * 发送下一页事件
   */
  emitNextPage(): void {
    eventBus.emit('ppt:nextPage');
  },

  /**
   * 发送上一页事件
   */
  emitPrevPage(): void {
    eventBus.emit('ppt:prevPage');
  },
  /**
   * 发送下一帧动画事件
   */
  emitNextAnim(): void {
    eventBus.emit('ppt:nextAnim');
  },

  /**
   * 发送上一帧动画事件
   */
  emitPrevAnim(): void {
    eventBus.emit('ppt:prevAnim');
  },

  /**
   * 发送跳转到指定页事件
   */
  emitGoToPage(pageIndex: number): void {
    eventBus.emit('ppt:goToPage', { pageIndex });
  },

  /**
   * 请求获取页面信息
   */
  requestPageInfo(): Promise<{ pageIndex: number; totalCount: number }> {
    return new Promise((resolve) => {
      const handler = (data: { pageIndex: number; totalCount: number }) => {
        eventBus.off('ppt:pageInfoResponse', handler);
        resolve(data);
      };
      eventBus.on('ppt:pageInfoResponse', handler);
      eventBus.emit('ppt:getPageInfo');
    });
  },

  /**
   * 响应页面信息请求
   */
  responsePageInfo(pageIndex: number, totalCount: number): void {
    eventBus.emit('ppt:pageInfoResponse', { pageIndex, totalCount });
  },

  /**
   * 监听下一页事件
   */
  onNextPage(handler: () => void): void {
    eventBus.on('ppt:nextPage', handler);
  },

  /**
   * 监听上一页事件
   */
  onPrevPage(handler: () => void): void {
    eventBus.on('ppt:prevPage', handler);
  },
  /**
   * 监听下一帧动画事件
   */
  onNextAnim(handler: () => void): void {
    eventBus.on('ppt:nextAnim', handler);
  },

  /**
   * 监听上一帧动画事件
   */
  onPrevAnim(handler: () => void): void {
    eventBus.on('ppt:prevAnim', handler);
  },

  /**
   * 监听跳转到指定页事件
   */
  onGoToPage(handler: (data: { pageIndex: number }) => void): void {
    eventBus.on('ppt:goToPage', handler);
  },

  /**
   * 监听获取页面信息请求
   */
  onGetPageInfo(handler: () => void): void {
    eventBus.on('ppt:getPageInfo', handler);
  },

  /**
   * 移除下一页事件监听
   */
  offNextPage(handler?: () => void): void {
    eventBus.off('ppt:nextPage', handler);
  },

  /**
   * 移除上一页事件监听
   */
  offPrevPage(handler?: () => void): void {
    eventBus.off('ppt:prevPage', handler);
  },
  /**
   * 移除下一页事件监听
   */
  offNextAnim(handler?: () => void): void {
    eventBus.off('ppt:nextAnim', handler);
  },

  /**
   * 移除上一页事件监听
   */
  offPrevAnim(handler?: () => void): void {
    eventBus.off('ppt:prevAnim', handler);
  },

  /**
   * 移除跳转到指定页事件监听
   */
  offGoToPage(handler?: (data: { pageIndex: number }) => void): void {
    eventBus.off('ppt:goToPage', handler);
  },

  /**
   * 移除获取页面信息请求监听
   */
  offGetPageInfo(handler?: () => void): void {
    eventBus.off('ppt:getPageInfo', handler);
  },

  /**
   * 清除所有事件监听
   */
  clear(): void {
    eventBus.clear();
  },
  /**
   * 预览文件事件
   * @param fileName
   * @param blob
   */
  emitPreviewByBlob(fileName: string, blob: Blob): void {
    eventBus.emit('ppt:previewByBlob', fileName, blob);
  },

  /**
   * 监听预览文件事件
   * @param handler (fileName: string, blob: Blob) => void
   */
  onPreviewByBlob(handler: (fileName: string, blob: Blob) => void) {
    eventBus.on('ppt:previewByBlob', handler);
  },
  /**
   * 移除预览文件事件监听
   *  @param handler (fileName: string, blob: Blob) => void
   */
  offPreviewByBlob(handler: (fileName: string, blob: Blob) => void) {
    eventBus.off('ppt:previewByBlob', handler);
  },

  /**
   * 预览文件事件 - Base64
   * @param chunkInfo ChunkInfo
   */
  emitPreviewByBase64(chunkInfo: ChunkInfo): void {
    eventBus.emit('ppt:previewByBase64', chunkInfo);
  },

  /**
   * 监听预览文件事件 - Base64
   * @param handler (chunkInfo: ChunkInfo) => void
   */
  onPreviewByBase64(handler: (chunkInfo: ChunkInfo) => void) {
    eventBus.on('ppt:previewByBase64', handler);
  },

  /**
   * 移除预览文件事件监听 - Base64
   *  @param handler (chunkInfo: ChunkInfo) => void
   */
  offPreviewByBase64(handler: (chunkInfo: ChunkInfo) => void) {
    eventBus.off('ppt:previewByBase64', handler);
  },

  /**
   * 发送下一页事件
   */
  emitJumpToPage(page: number): void {
    eventBus.emit('ppt:jumpToPage', page);
  },
  /**
   * 监听下一页事件
   */
  onJumpToPage(handler: (page: number) => void): void {
    eventBus.on('ppt:jumpToPage', handler);
  },
  /**
   * 移除下一页事件监听
   */
  offJumpToPage(handler?: (page: number) => void): void {
    eventBus.off('ppt:jumpToPage', handler);
  },

  /**
   * 强制刷新预览
   */
  emitForceReloadPreview(): void {
    eventBus.emit('ppt:forceReloadPreview');
  },
  /**
   * 监听强制刷新预览
   */
  onForceReloadPreview(handler: () => void): void {
    eventBus.on('ppt:forceReloadPreview', handler);
  },
  /**
   * 移除强制刷新预览
   */
  offForceReloadPreview(handler: () => void): void {
    eventBus.off('ppt:forceReloadPreview', handler);
  },
};
