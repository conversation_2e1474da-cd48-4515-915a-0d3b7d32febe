import type { Im } from '@amdox/types';

import { requestClient } from '#/api/request';

export namespace ImMemberApi {
  /** 获取群成员列表接口参数 */
  export interface GetMemberListParams {
    /** 群列表的groupId编号（群号） */
    groupId: string;
  }

  /** 获取群成员列表接口返回值 */
  export type GetMemberListResult = Im.GroupMemberDetail[];

  /** 删除群成员接口参数 */
  export type DelGroupMemberParams = Im.DelGroupMemberParams;
}

/**
 * 获取群成员列表
 */
export async function getMemberListApi(
  params: ImMemberApi.GetMemberListParams,
) {
  return requestClient.get<ImMemberApi.GetMemberListResult>(
    '/im/member/getMemberList',
    {
      params,
    },
  );
}

/**
 * 删除群成员
 */
export async function delGroupMemberApi(
  data: ImMemberApi.DelGroupMemberParams,
) {
  return requestClient.post('/im/member/delGroupMember', data);
}
