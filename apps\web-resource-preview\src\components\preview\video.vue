<script lang="ts" setup>
import { onMounted, onUnmounted } from 'vue';

import { useHiddenLoading } from '#/hooks/useLoading';

// 定义组件名称
defineOptions({ name: 'VideoPreview' });

// 定义 props
defineProps<{ src: string }>();

const emits = defineEmits<{
  (e: 'loading'): void;
}>();

const { bindEventListener, removeEventListener } = useHiddenLoading(emits);

onMounted(() => {
  bindEventListener('video', 'loadedmetadata');
});

onUnmounted(() => {
  removeEventListener('video', 'loadedmetadata');
});
</script>

<template>
  <div class="video-preview">
    <video :src="src" controls></video>
  </div>
</template>

<style scoped>
.video-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.video-preview video {
  width: 100%;
  height: 100%;
  object-fit: contain; /* 使用 contain 来确保视频内容完整可见，而非裁剪 */
}
</style>
