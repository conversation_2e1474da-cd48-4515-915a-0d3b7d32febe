/**
 * @description:下载文件（base64数据）
 * @param {string} dataUrl base64数据
 * @param {string} fileName 文件名称
 * @param {string} fileExtension  文件后缀
 * @return {*}
 */
export function downloadBase64(
  dataUrl: string,
  fileName: string = 'download',
  fileExtension: string = 'png',
) {
  // 创建 Blob
  const blob = base64ToBlob(dataUrl);
  if (!blob) return;
  // 触发下载
  const blobUrl = URL.createObjectURL(blob);
  const createDom = document.createElement('a');
  document.body.append(createDom);
  createDom.href = blobUrl;
  createDom.download = `${fileName}.${fileExtension}`;
  createDom.click();
  createDom.remove();
  URL.revokeObjectURL(blobUrl);
}

/**
 * @description:base64转blob
 * @param {string} base64 base64数据
 * @return {*}
 */
export function base64ToBlob(base64: string): Blob | null {
  // 检查 dataUrl 是否包含 base64 标识符（如 "data:image/png;base64,"）
  if (!base64.includes(',')) {
    console.error('无效的 dataUrl 格式，缺少逗号分隔符');
    return null;
  }

  // 安全获取 base64 数据部分
  const base64Data = base64.split(',')[1];

  if (!base64Data) {
    console.error('无法提取 base64 数据');
    return null;
  }

  // 解码 base64 数据
  const byteCharacters = atob(base64Data);
  const byteArrays = [];
  for (let offset = 0; offset < byteCharacters.length; offset += 10_240) {
    const slice = byteCharacters.slice(offset, offset + 10_240);
    const byteNumbers = Array.from({ length: slice.length });
    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.codePointAt(i);
    }
    if (isNumberArray(byteNumbers)) {
      const byteArray = new Uint8Array(byteNumbers as number[]);
      byteArrays.push(byteArray);
    } else {
      throw new Error('byteNumbers 不是一个 number[] 类型');
    }
  }

  const blob = new Blob(byteArrays, { type: 'application/octet-stream' });
  return blob;
}

function isNumberArray(arr: unknown[]): arr is number[] {
  return arr.every((item) => typeof item === 'number');
}
