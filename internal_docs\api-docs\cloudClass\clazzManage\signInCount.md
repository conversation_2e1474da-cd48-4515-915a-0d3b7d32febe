# 签到统计

## 接口信息

- **请求方法**：GET
- **接口地址**：/cloudClass/clazzManage/signInCount

#### 请求参数

| 字段      | 类型    | 是否必传 | 描述         |
| --------- | ------- | -------- | ------------ |
| checkUuid | String  | 是       | 事件考勤uuid |
| schoolId  | Integer | 是       | 学校id       |
| clazzId   | Integer | 是       | 班级id       |

#### 响应参数

| 字段                          | 类型    | 是否必传 | 描述           |
| ----------------------------- | ------- | -------- | -------------- |
| signInStudentList             | Object  | 是       | 签到学生集合   |
| signIn                        | Integer | 是       | 签到人数       |
| unSignIn                      | Integer | 是       | 未签到人数     |
| unSignInStudentList           | Object  | 是       | 未签到学生集合 |
| unSignInStudentList.studentId | Integer | 是       | 学生id         |
| unSignInStudentList.schoolId  | Integer | 是       | 学校id         |

#### 响应示例

{ "signInStudentList": {}, "signIn": 0, "unSignIn": 62, "unSignInStudentList": [ { "studentId": 10010833, "schoolId": 10000112 } ] }
