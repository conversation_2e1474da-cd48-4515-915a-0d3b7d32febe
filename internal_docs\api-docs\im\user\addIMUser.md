# 批量添加IM账号信息

## 接口信息

- **请求方法**：POST
- **接口地址**：https://test-im.amdox.com.cn/im/user/addIMUser

#### 请求参数

| 字段         | 类型   | 是否必传 | 描述             |
| ------------ | ------ | -------- | ---------------- |
| imUserId     | string | 是       | IM的账号         |
| userName     | string | 是       | 名称             |
| userIconPath | string | 是       | 用户头像存放路径 |

#### 请求示例

```json
[
  {
    "imUserId": "TEST_TEACHER_10011917",
    "userName": "ywy",
    "userIconPath": "https://test.amdox.com.cn/static/school_v5/0/0/12/teachingAssistant/data/10011917/other/2025-03-19/801cb87d-a79e-48d5-8d36-4fdfd90fbe88.png"
  },
  {
    "imUserId": "STUDENT_10048095",
    "userName": "刘备",
    "userIconPath": "http://picture.amdox.com.cn/static/defaultPhoto.png"
  }
]
```

#### 响应参数

未提供具体的响应参数信息。
