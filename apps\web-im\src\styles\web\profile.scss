/* stylelint-disable-next-line selector-class-pattern */
.TUI-profile {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #fff;

  &-basic {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    overflow: hidden;

    &-avatar {
      width: 30px;
      height: 30px;
      margin-right: 10px;
      border-radius: 5px;
    }

    &-info {
      box-sizing: border-box;
      display: flex;
      flex: 1;
      flex-direction: column;
      overflow: hidden;
      font-size: 14px;

      &-nick {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &-id {
        display: flex;
        flex-direction: row;
        overflow: hidden;

        &-label {
          font-weight: 400;
          color: #999;
        }

        &-value {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  &-setting {
    &-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 40px;
      padding-right: 8px;
      padding-left: 14px;
      line-height: 40px;
      text-align: center;
      cursor: pointer;

      &:hover {
        background-color: rgb(0 110 255 / 10%);

        /* stylelint-disable-next-line selector-class-pattern */
        .TUI-profile-setting-item-children {
          display: block;
        }
      }

      &-label {
        display: flex;
        flex: 1;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
      }

      /* stylelint-disable-next-line no-descending-specificity */
      &-children {
        position: absolute;
        left: 100%;
        z-index: 2;
        display: none;
        min-width: 167px;
        background: #fff;
        border-radius: 0 4px 4px 0;
        box-shadow: 2px 1px 6px 0 rgb(2 16 43 / 15%);

        &-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 40px;
          padding-right: 8px;
          padding-left: 14px;
          line-height: 40px;
          text-align: center;
          cursor: pointer;

          &:hover {
            background-color: rgb(0 110 255 / 10%);
          }

          &-label {
            display: flex;
            flex: 1;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            padding-right: 5px;
            font-size: 14px;
          }
        }
      }
    }
  }
}
