import type { UsbItem, WebviewProvide } from '@amdox/webview';

export class QtProvide implements WebviewProvide {
  /**
   * 应用安装成功
   * @param data
   * @returns
   */
  public app_install_success(_data: string): Promise<void> {
    return Promise.resolve();
  }

  /**
   * 更新下载进度
   * @param data
   * @returns
   */
  public update_download_progress(_data: string): Promise<void> {
    return Promise.resolve();
  }

  /**
   * 更新语言
   * @param _data
   * @returns
   */
  public update_language(_data: string): Promise<void> {
    return Promise.resolve();
  }

  /**
   * 更新 USB
   * @param usbData
   * @returns
   */
  public updateUsb(_usbData: UsbItem[]): Promise<void> {
    return Promise.resolve();
  }
}
