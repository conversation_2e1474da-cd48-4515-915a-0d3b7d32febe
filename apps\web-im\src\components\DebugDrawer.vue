<script setup lang="ts">
import type { SupportedLanguagesType } from '@amdox/types';

import { ref } from 'vue';

import { loadLocaleMessages } from '@amdox/locales';
import { preferences } from '@amdox/preferences';

import { ElMessage, ElOption, ElSelect } from 'element-plus';

const languages: { label: string; value: SupportedLanguagesType }[] = [
  { label: '简体中文', value: 'zh-CN' },
  { label: '繁體中文', value: 'zh-TW' },
  { label: 'English', value: 'en-US' },
  { label: 'Español', value: 'es-ES' },
  { label: 'Русский', value: 'ru-RU' },
  { label: 'हिन्दी', value: 'hi-IN' },
  { label: 'العربية', value: 'ar-SA' },
  { label: 'বাংলা', value: 'bn-BD' },
  { label: 'Français', value: 'fr-FR' },
  { label: 'Bahasa Indonesia', value: 'id-ID' },
  { label: '日本語', value: 'ja-JP' },
  { label: '한국어', value: 'ko-KR' },
  { label: 'Bahasa Melayu', value: 'ms-MY' },
  { label: 'ไทย', value: 'th-TH' },
  { label: 'Tiếng Việt', value: 'vi-VN' },
];

const currentLanguage = ref<SupportedLanguagesType>(preferences.app.locale);

async function switchLanguage(lang: SupportedLanguagesType) {
  try {
    await loadLocaleMessages(lang);
    preferences.app.locale = lang;
    currentLanguage.value = lang; // Update the selected value
    ElMessage.success(`语言已切换为: ${lang}`);
  } catch (error) {
    console.error('[DebugDrawer] Failed to switch language:', error);
    ElMessage.error('语言切换失败');
  }
}
</script>

<template>
  <div class="debug-drawer">
    <div class="debug-drawer-content">
      <h3>调试工具</h3>
      <div class="feature-group">
        <h4>切换语言</h4>
        <ElSelect
          v-model="currentLanguage"
          placeholder="Select Language"
          @change="switchLanguage"
        >
          <ElOption
            v-for="lang in languages"
            :key="lang.value"
            :label="lang.label"
            :value="lang.value"
          />
        </ElSelect>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.debug-drawer {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 1000;
  width: 250px;
  height: 100vh;
  padding: 20px;
  overflow-y: auto;
  color: #303133;
  background-color: #f0f2f5;
  border-left: 1px solid #dcdfe6;
  box-shadow: -2px 0 8px rgb(0 0 0 / 15%);
}

.debug-drawer-content h3 {
  padding-bottom: 10px;
  margin-bottom: 20px;
  font-size: 18px;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
}

.feature-group {
  margin-bottom: 20px;
}

.feature-group h4 {
  margin-bottom: 10px;
  font-size: 16px;
  color: #606266;
}
</style>
