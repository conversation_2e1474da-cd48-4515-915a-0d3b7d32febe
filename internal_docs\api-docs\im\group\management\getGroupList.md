# 群列表

## 接口信息

- **请求方法**: POST
- **接口地址**: `/im/group/getGroupList`

#### 请求参数

| 字段      | 类型   | 是否必传 | 描述     |
| --------- | ------ | -------- | -------- |
| studentId | string | 否       | 学生编号 |

#### 请求示例

```json
{
  "studentId": ""
}
```

#### 响应参数

| 字段 | 类型 | 是否必传 | 描述 |
| --- | --- | --- | --- |
| groupId | string | 是 | 群编号 |
| ownerAccount | string | 是 | 群主编号 |
| groupName | string | 是 | 群名称 |
| introduction | string | 是 | 群简介 |
| notification | string | 是 | 群公告 |
| faceUrl | string | 是 | 群头像 |
| muteAllMember | string | 是 | 设置全员禁言(选填):"On"开启，"Off"关闭 |
| addFriendsType | number | 是 | 开放加入群聊 1-开启 0关闭 |
| joinGroupType | number | 是 | 允许互相添加好友 1-开启 0关闭 |
| memberEditType | number | 是 | 允许成员修改昵称 1-开启 0关闭 |
| memberNum | number | 是 | 成员数量 |
| memberNameCard | string | 是 | 群成员群名片 |
| groupType | number | 是 | 1班级群 2自定义群 3私聊 |
| memberList | array | 是 | 成员列表 |
| memberList.imUserId | string | 是 | 成员编号 |
| memberList.userName | string | 是 | 成员名称 |
| memberList.userIconPath | string | 是 | 用户头像存放路径 |
| memberList.role | string | 是 | Owner 群主、Admin 群管理员以及 Member 群成员 |
| memberList.identityType | string | 是 | 1-教师 3-学生 |
| memberList.subjectId | string | 是 | 科目编号 |
| memberList.joinTime | string | 是 | 加入时间 |
| memberList.muteTime | string | 是 | 群成员的禁言时间，单位为秒 0不禁言 |

#### 响应示例

```json
{
  "groupId": "10000",
  "ownerAccount": "TEACHER_10011917",
  "groupName": "一年级1班",
  "introduction": "欢迎加入一年级一班",
  "notification": "66666",
  "faceUrl": "https://test.amdox.com.cn/static/school_v5/0/0/12/teachingAssistant/data/********/courseware/2025-06-03/1.png",
  "muteAllMember": "Off",
  "addFriendsType": 1,
  "joinGroupType": 1,
  "memberEditType": 1,
  "memberNum": 3,
  "memberNameCard": "尹",
  "groupType": 3,
  "memberList": [
    {
      "imUserId": "TEACHER_10011917",
      "userName": "尹",
      "userIconPath": "",
      "role": "Owner",
      "identityType": "1",
      "subjectId": "",
      "joinTime": "**********",
      "muteTime": "**********"
    },
    {
      "imUserId": "CLASS_S_1_10011726",
      "userName": "李四",
      "userIconPath": "",
      "role": "Member",
      "identityType": "",
      "subjectId": "",
      "joinTime": "**********",
      "muteTime": "0"
    },
    {
      "imUserId": "CLASS_S_1_10011725",
      "userName": "张三",
      "userIconPath": "",
      "role": "Member",
      "identityType": "3",
      "subjectId": "",
      "joinTime": "1737352789",
      "muteTime": "0"
    }
  ]
}
```
