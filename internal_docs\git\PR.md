# Monorepo 项目 Git 分支管理策略文档

## 目录

- [1. 概述](#1-概述)
- [2. 分支架构设计](#2-分支架构设计)
- [3. 核心工作流程](#3-核心工作流程)
- [4. 特殊场景处理](#4-特殊场景处理)
- [5. 版本管理策略](#5-版本管理策略)
- [6. 最佳实践](#6-最佳实践)
- [7. 故障处理指南](#7-故障处理指南)
- [8. 工具与自动化](#8-工具与自动化)

## 1. 概述

### 1.1 文档目标

本文档定义了适用于 Monorepo 多人协作开发的 Git 分支管理策略，旨在：

- 🎯 提供清晰的分支管理规范和工作流程
- 🔄 确保团队协作的高效性和代码质量
- 📦 支持多版本并行开发和维护
- 🚀 实现自动化的发布和部署流程

### 1.2 适用场景

- 多人协作开发同一特性
- Monorepo 项目架构
- 需要支持多版本并行维护
- 对代码质量和发布流程有严格要求的项目

### 1.3 管理原则

- **代码隔离**：通过分支隔离不同开发阶段的代码
- **集成效率**：平衡代码隔离与集成效率
- **质量保证**：确保主分支代码的稳定性和质量
- **流程标准化**：统一的工作流程和规范

## 2. 分支架构设计

### 2.1 分支层级结构

```
master (生产环境)
├─ release/v{major}.{minor}.{patch} (版本发布分支)
│  └─ patch/v{major}.{minor}.{patch}-{description} (补丁分支)
├─ hotfix/{description} (紧急修复分支)
└─ develop (开发主分支)
   └─ feature/{feature-name} (主特性分支)
      ├─ {username}/{feature-name} (个人特性分支)
      └─ {username}/{feature-name}-{subtask} (个人子任务分支，可选)
```

### 2.2 分支定义与职责

#### 2.2.1 核心分支

| 分支名称  | 用途         | 生命周期 | 合并来源        | 合并目标 |
| --------- | ------------ | -------- | --------------- | -------- |
| `master`  | 生产环境代码 | 永久     | release, hotfix | -        |
| `develop` | 开发主分支   | 永久     | feature         | release  |

#### 2.2.2 功能分支

| 分支名称               | 用途       | 生命周期   | 合并来源 | 合并目标 |
| ---------------------- | ---------- | ---------- | -------- | -------- |
| `feature/{name}`       | 主特性开发 | 特性完成前 | 个人分支 | develop  |
| `{username}/{feature}` | 个人开发   | 任务完成前 | -        | feature  |

#### 2.2.3 维护分支

| 分支名称 | 用途 | 生命周期 | 合并来源 | 合并目标 |
| --- | --- | --- | --- | --- |
| `release/v{version}` | 版本发布准备 | 版本维护期 | develop | master, develop |
| `hotfix/{description}` | 紧急修复 | 修复完成前 | master | master, develop |
| `patch/v{version}-{desc}` | 版本补丁 | 补丁完成前 | release | release |

### 2.3 分支命名规范

#### 2.3.1 命名格式

```bash
# 特性分支
feature/user-dashboard           # 主特性分支
alice/user-dashboard            # 个人特性分支
alice/user-dashboard-api        # 个人子任务分支

# 版本分支
release/v2.1.0                  # 版本发布分支
patch/v2.1.1-login-fix          # 补丁分支

# 修复分支
hotfix/critical-login-bug       # 紧急修复分支
hotfix/v1.2.1                  # 版本号修复分支
```

#### 2.3.2 命名约定

- 使用小写字母和连字符
- 特性名称要简洁明确
- 版本号遵循语义化版本规范
- 避免使用特殊字符和空格

## 3. 核心工作流程

### 3.1 特性开发工作流程

#### 3.1.1 启动特性开发

```bash
# 1. 项目负责人创建主特性分支
git checkout develop
git pull origin develop
git checkout -b feature/user-dashboard
git push origin feature/user-dashboard

# 2. 团队成员创建个人分支
git checkout feature/user-dashboard
git pull origin feature/user-dashboard
git checkout -b alice/user-dashboard
git push origin alice/user-dashboard
```

#### 3.1.2 日常开发流程

```bash
# 1. 同步主特性分支更新（每日执行）
git checkout alice/user-dashboard
git pull origin feature/user-dashboard

# 2. 进行开发工作
# ... 开发代码 ...

# 3. 提交代码（遵循提交规范）
git add .
git commit -m "feat(dashboard): add user profile section"
git push origin alice/user-dashboard
```

#### 3.1.3 特性集成流程

```bash
# 1. 提交 Pull Request
# 创建从 alice/user-dashboard 到 feature/user-dashboard 的 PR

# 2. 代码审查通过后合并
# 使用 "Squash and Merge" 方式合并

# 3. 特性完成后合并到 develop
git checkout develop
git pull origin develop
git merge --no-ff feature/user-dashboard
git push origin develop

# 4. 清理分支
git branch -d feature/user-dashboard alice/user-dashboard
git push origin --delete feature/user-dashboard alice/user-dashboard
```

### 3.2 提交规范

#### 3.2.1 提交消息格式

```
<类型>(<范围>): <描述>

[可选的正文]

[可选的脚注]
```

#### 3.2.2 类型定义

| 类型 | 说明 | 示例 |
| --- | --- | --- |
| `feat` | 新功能 | `feat(dashboard): add user profile widget` |
| `fix` | 修复bug | `fix(auth): resolve login timeout issue` |
| `refactor` | 重构 | `refactor(api): optimize data fetching logic` |
| `docs` | 文档更新 | `docs(readme): update installation guide` |
| `style` | 代码格式 | `style(component): fix indentation` |
| `test` | 测试相关 | `test(utils): add unit tests for helpers` |
| `chore` | 构建过程或辅助工具 | `chore(deps): update dependencies` |

#### 3.2.3 范围定义

常用范围包括：

- `dashboard`, `auth`, `api`, `ui`, `utils`, `config`, `docs`
- 对应项目中的模块或功能区域

## 4. 特殊场景处理

### 4.1 紧急修复（Hotfix）流程

#### 4.1.1 使用场景

- 生产环境出现严重bug需要立即修复
- 安全漏洞需要紧急修补
- 关键功能故障影响业务运行

#### 4.1.2 操作流程

```bash
# 1. 创建hotfix分支
git checkout master
git pull origin master
git checkout -b hotfix/critical-login-bug
git push origin hotfix/critical-login-bug

# 2. 进行紧急修复
# ... 修复代码 ...
git add .
git commit -m "fix(auth): resolve critical login authentication bug"
git push origin hotfix/critical-login-bug

# 3. 测试验证
# 部署到测试环境进行验证

# 4. 合并到master
git checkout master
git merge --no-ff hotfix/critical-login-bug
git tag -a v1.2.1 -m "Hotfix: Fix critical login bug"
git push origin master v1.2.1

# 5. 合并到develop
git checkout develop
git merge --no-ff hotfix/critical-login-bug
git push origin develop

# 6. 清理分支
git branch -d hotfix/critical-login-bug
git push origin --delete hotfix/critical-login-bug
```

#### 4.1.3 注意事项

- ⚠️ 只修复紧急问题，不添加新功能
- ⚠️ 必须进行充分测试验证
- ⚠️ 需要通知所有开发人员同步代码
- ⚠️ 修复后应分析根本原因，制定预防措施

### 4.2 冲突处理策略

#### 4.2.1 预防措施

```bash
# 1. 每日同步主分支更新
git pull origin feature/main-branch

# 2. 小步提交，频繁集成
git add . && git commit -m "feat: implement small feature"
git push origin personal-branch

# 3. 明确分工，减少文件冲突
# 团队协商，避免多人同时修改同一文件
```

#### 4.2.2 冲突解决流程

```bash
# 1. 拉取最新代码时出现冲突
git pull origin feature/main-branch
# Auto-merging src/components/Header.vue
# CONFLICT (content): Merge conflict in src/components/Header.vue

# 2. 手动解决冲突
# 编辑冲突文件，选择保留的代码
# 移除冲突标记 <<<<<<< ======= >>>>>>>

# 3. 提交解决后的代码
git add src/components/Header.vue
git commit -m "resolve: merge conflict in Header component"
git push origin personal-branch
```

#### 4.2.3 责任划分

- 由最后修改相关代码的开发者负责解决冲突
- 无法确定责任人时，由团队协商解决
- 复杂冲突需要相关开发者共同参与解决

## 5. 版本管理策略

### 5.1 语义化版本控制

#### 5.1.1 版本号格式

```
主版本号.次版本号.修订号 (MAJOR.MINOR.PATCH)
```

#### 5.1.2 版本递增规则

| 版本类型 | 递增条件           | 示例          |
| -------- | ------------------ | ------------- |
| MAJOR    | 不兼容的API修改    | 1.0.0 → 2.0.0 |
| MINOR    | 向下兼容的功能新增 | 1.0.0 → 1.1.0 |
| PATCH    | 向下兼容的问题修正 | 1.0.0 → 1.0.1 |

#### 5.1.3 预发布版本

```bash
# Alpha版本（内部测试）
v2.0.0-alpha.1

# Beta版本（公开测试）
v2.0.0-beta.1

# RC版本（发布候选）
v2.0.0-rc.1
```

### 5.2 版本发布流程

#### 5.2.1 创建发布分支

```bash
# 1. 从develop创建版本分支
git checkout develop
git pull origin develop
git checkout -b release/v2.1.0
git push origin release/v2.1.0

# 2. 更新版本信息
npm version 2.1.0 --no-git-tag-version
git add package.json
git commit -m "chore(release): bump version to 2.1.0"
git push origin release/v2.1.0
```

#### 5.2.2 发布准备与测试

```bash
# 1. 部署到预发布环境
npm run deploy:staging

# 2. 进行全面测试
npm run test:full
npm run test:e2e

# 3. 修复发现的问题
git add .
git commit -m "fix(release): resolve issue in v2.1.0"
git push origin release/v2.1.0
```

#### 5.2.3 正式发布

```bash
# 1. 合并到master
git checkout master
git pull origin master
git merge --no-ff release/v2.1.0

# 2. 创建版本标签
git tag -a v2.1.0 -m "Release version 2.1.0"
git push origin master v2.1.0

# 3. 同步到develop
git checkout develop
git merge --no-ff release/v2.1.0
git push origin develop

# 4. 部署到生产环境
npm run deploy:production
```

### 5.3 多版本维护策略

#### 5.3.1 补丁发布流程

```bash
# 1. 在对应版本分支创建补丁分支
git checkout release/v2.1.0
git pull origin release/v2.1.0
git checkout -b patch/v2.1.1-security-fix

# 2. 进行修复
git add .
git commit -m "fix(security): patch XSS vulnerability"
git push origin patch/v2.1.1-security-fix

# 3. 合并到版本分支
git checkout release/v2.1.0
git merge patch/v2.1.1-security-fix

# 4. 发布补丁版本
git tag -a v2.1.1 -m "Security patch: fix XSS vulnerability"
git push origin release/v2.1.0 v2.1.1
```

## 6. 最佳实践

### 6.1 代码质量保证

#### 6.1.1 Pull Request 规范

```markdown
## PR 标题格式

[类型] 功能名称: 简要描述

## PR 描述模板

### 变更内容

- 新增功能A
- 修复问题B
- 优化性能C

### 测试情况

- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 手动测试完成

### 影响范围

- 影响模块：dashboard, auth
- 破坏性变更：无
- 数据库变更：无
```

#### 6.1.2 代码审查要点

- ✅ 代码逻辑正确性
- ✅ 代码风格一致性
- ✅ 测试覆盖率
- ✅ 性能影响评估
- ✅ 安全性检查
- ✅ 文档更新

#### 6.1.3 自动化检查

```bash
# 代码格式检查
npm run lint

# 类型检查
npm run type-check

# 单元测试
npm run test

# 构建检查
npm run build
```

### 6.2 分支管理规范

#### 6.2.1 分支生命周期管理

- 📅 定期清理已合并的分支
- 📊 监控长期存在的特性分支
- 🔄 及时同步上游分支更新
- 📋 维护分支状态文档

### 6.3 团队协作规范

#### 6.3.1 沟通机制

- 📢 每日站会同步开发进度
- 📝 重要变更及时通知团队
- 🔄 定期技术分享和经验总结
- 📋 维护团队开发文档

#### 6.3.2 责任分工

| 角色       | 职责                       |
| ---------- | -------------------------- |
| 项目负责人 | 创建主特性分支，审核重要PR |
| 开发人员   | 负责个人分支开发，解决冲突 |
| 运维人员   | 部署发布，环境维护         |

## 7. 故障处理指南

### 7.1 常见问题排查

#### 7.1.1 分支同步问题

```bash
# 问题：个人分支与主分支差距过大
# 解决方案1：重新基于主分支创建
git checkout feature/main-branch
git pull origin feature/main-branch
git checkout -b username/feature-new
git cherry-pick <commit-hash>  # 选择性合并提交

# 解决方案2：交互式变基
git checkout username/feature
git rebase -i feature/main-branch
```

#### 7.1.2 提交历史问题

```bash
# 问题：提交历史混乱
# 解决方案：整理提交历史
git rebase -i HEAD~5  # 整理最近5个提交
# 在编辑器中选择 squash, fixup, reword 等操作

# 问题：错误提交到错误分支
# 解决方案：移动提交到正确分支
git log --oneline  # 找到提交hash
git checkout correct-branch
git cherry-pick <commit-hash>
git checkout wrong-branch
git reset --hard HEAD~1  # 删除错误提交
```

#### 7.1.3 合并冲突处理

```bash
# 复杂冲突处理工具
git mergetool  # 使用可视化工具解决冲突

# 查看冲突文件状态
git status
git diff

# 中断合并（如果需要）
git merge --abort
git rebase --abort
```

### 7.2 紧急情况处理

#### 7.2.1 版本回滚

```bash
# 快速回滚到上一版本
git checkout master
git revert HEAD  # 撤销最新提交
git push origin master

# 回滚到指定版本
git checkout master
git reset --hard v2.0.0  # 危险操作，慎用
git push origin master --force-with-lease
```

#### 7.2.2 数据恢复

```bash
# 恢复删除的分支
git reflog  # 查看引用日志
git checkout -b recovered-branch <commit-hash>

# 恢复删除的提交
git reflog
git cherry-pick <commit-hash>

# 恢复删除的文件
git checkout HEAD~1 -- <file-path>
```

---

## 📋 检查清单

### 开发前检查

- [ ] 已从正确的上游分支创建个人分支
- [ ] 本地环境配置正确
- [ ] 了解特性需求和技术方案

### 开发中检查

- [ ] 每日同步上游分支更新
- [ ] 遵循提交消息规范
- [ ] 进行单元测试
- [ ] 代码符合项目规范

### 提交前检查

- [ ] 代码审查自检通过
- [ ] 所有测试通过
- [ ] 文档已更新
- [ ] 无明显性能问题

### 发布前检查

- [ ] 版本号正确递增
- [ ] 变更日志已更新
- [ ] 全面测试通过
- [ ] 生产环境准备就绪

---

通过遵循本文档的分支管理策略和工作流程，团队可以实现：

- 🎯 **高效协作**：清晰的分支结构和工作流程
- 🔒 **质量保证**：严格的代码审查和测试流程
- 📦 **版本管理**：完善的版本发布和维护策略
- 🚀 **持续集成**：自动化的构建、测试和部署流程
