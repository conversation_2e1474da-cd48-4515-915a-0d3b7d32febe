# 获取学校信息

## 接口信息

- **请求方法**：GET
- **接口地址**：/cloudClass/clazzManage/findSchoolInfo

#### 请求参数

无

#### 请求示例

无

#### 响应参数

| 字段           | 类型   | 是否必传 | 描述         |
| -------------- | ------ | -------- | ------------ |
| schoolId       | Number | 是       | 学校ID       |
| schoolName     | String | 是       | 学校名称     |
| schoolLogoPath | String | 是       | 学校Logo路径 |
| teacherId      | Number | 是       | 教师ID       |
| userId         | Number | 是       | 用户ID       |

#### 响应示例

```json
[
  {
    "schoolId": 10000112,
    "schoolName": "青岛展会学校",
    "schoolLogoPath": "http://test.amdox.com.cn/static/school_v5/0/0/common_files/21/202207514593779750357school",
    "teacherId": 10002463,
    "userId": 10013277
  },
  {
    "schoolId": 10000199,
    "schoolName": "DYL测试走班排课专用新学校",
    "schoolLogoPath": "http://test.amdox.com.cn/static/cloud_class/common_files/21/20220119172156475904463school",
    "teacherId": 10002474,
    "userId": 10013277
  }
]
```
