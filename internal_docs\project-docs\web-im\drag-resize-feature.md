# 聊天界面拖拽调整功能优化文档

## 功能概述

在 TUIChat 组件中实现了可拖拽的分隔条功能，允许用户通过鼠标上下拖动来调整 MessageList 和 MessageInput 区域的大小。

## 优化内容

### 问题描述

在拖动过程中，当 MessageInput 高度变小时，MessageInputToolbar 中的按钮组件会被挤压或遮挡。

### 解决方案

1. **布局结构优化**

   - 保持 MessageInputToolbar 的固定高度（48px）
   - 新增 `tui-chat-message-input-wrapper` 包装层来控制 MessageInput 的可用空间
   - 确保 MessageInputButton 始终可见

2. **高度计算逻辑**

   ```typescript
   // 计算 MessageInput 包装器的高度
   height: Math.max(
     messageInputHeight - (isInputToolbarShow ? toolbarHeight : 0),
     minMessageInputHeight - (isInputToolbarShow ? toolbarHeight : 0),
   );
   ```

3. **滚动处理**
   - 当内容高度超过容器高度时，自动显示滚动条
   - 设置 `min-height: 0` 允许 flex 容器收缩
   - 使用 `overflow-y: auto` 在需要时显示垂直滚动条

### 代码变更

#### 1. TUIChat 主组件 (`packages/effects/chat-ui/src/components/TUIChat/index.vue`)

**新增状态变量：**

```typescript
const toolbarHeight = 48; // 工具栏固定高度
const minMessageInputHeight = 130; // 增加最小高度，确保工具栏 + 按钮可见
```

**布局结构调整：**

```vue
<div class="tui-chat-input-container">
  <MessageInputToolbar />
  <div class="tui-chat-message-input-wrapper">
    <MessageInput />
  </div>
</div>
```

#### 2. 样式优化 (`packages/effects/chat-ui/src/components/TUIChat/style/web.scss`)

**新增样式类：**

```scss
&-message-input-wrapper {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 80px; // 设置最小高度确保按钮可见

  .message-input-wrapper {
    overflow-y: auto;
    flex: 1;
    min-height: 0;
  }
}
```

#### 3. MessageInput 组件优化 (`packages/effects/chat-ui/src/components/TUIChat/message-input/index.vue`)

**样式调整：**

```scss
.message-input-container {
  min-height: 0; // 允许容器收缩
}
```

## 功能特性

1. **固定工具栏高度**：MessageInputToolbar 始终保持 48px 高度
2. **自适应滚动**：当内容超出容器时自动显示滚动条
3. **最小高度保护**：确保 MessageInputButton 始终可见
4. **流畅拖拽体验**：保持原有的拖拽交互逻辑
5. **设置持久化**：用户的布局设置仍然会保存到本地存储

## 使用说明

1. 在 PC 端打开聊天界面
2. 拖动 MessageList 和 MessageInput 之间的分隔条
3. 当 MessageInput 区域高度减小时：
   - MessageInputToolbar 保持固定高度
   - MessageInput 内容区域会显示滚动条
   - MessageInputButton 始终可见和可用

## 技术要点

- 使用 CSS Flexbox 布局实现响应式设计
- 通过 `min-height: 0` 允许 flex 子项收缩
- 合理设置 `overflow` 属性实现滚动效果
- 动态计算高度确保组件间的协调
