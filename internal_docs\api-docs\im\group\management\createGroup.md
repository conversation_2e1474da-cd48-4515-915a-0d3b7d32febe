# 创建群

## 接口信息

- **请求方法**：POST
- **接口地址**：/im/group/management/createGroup

#### 请求参数

| 字段 | 类型 | 是否必传 | 描述 |
| --- | --- | --- | --- |
| studentId | string | 否 | 学生id |
| clazzId | string | 否 | 班级id |
| groupName | string | 是 | 群名称 |
| groupType | number | 是 | 1班级群 2自定义班级群 3私聊 |
| faceUrl | string | 是 | 群头像地址 |
| introduction | string | 否 | 群简介 |
| dataList | array | 是 | 群自定义字段 |
| dataList.key | string | 是 | 值add_friends_type(群是否能加好友)、join_group_type(群是否开启加入)、member_edit_type(群成员是否群卡片) |
| dataList.value | string | 是 | 值 1-开启 0关闭 |
| memberList | array | 是 | 群成员列表 |
| memberList.imUserId | string | 否 | 群成员im编号 |
| memberList.voList | array | 否 | 群成员自定义 |
| memberList.voList.key | string | 否 | 值identityType 、subjectId |
| memberList.voList.value | string | 否 | 值identityType(1-教师 2学生) subjectId 学科编号 |

#### 请求示例

```json
{
  "studentId": "",
  "clazzId": "10002119",
  "groupName": "一年级一班",
  "groupType": 1,
  "faceUrl": "https://test.amdox.com.cn/static/school_v5/0/0/12/teachingAssistant/data/10011917/other/2025-03-19/801cb87d-a79e-48d5-8d36-4fdfd90fbe88.png",
  "introduction": "一年级一班的群",
  "dataList": [
    {
      "key": "add_friends_type",
      "value": "1"
    },
    {
      "key": "join_group_type",
      "value": "1"
    },
    {
      "key": "member_edit_type",
      "value": "1"
    }
  ],
  "memberList": [
    {
      "imUserId": "",
      "voList": [
        {
          "key": "",
          "value": ""
        }
      ]
    }
  ]
}
```

#### 响应参数

| 字段  | 类型   | 描述 |
| ----- | ------ | ---- |
| 10003 | string | 群id |

#### 响应示例

```json
"10003"
```
