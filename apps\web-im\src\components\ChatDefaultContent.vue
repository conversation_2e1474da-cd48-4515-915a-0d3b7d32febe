<script setup lang="ts">
import { SvgIcon } from '@amdox/icons';

import defaultContentIcon from '#/assets/icon/chat/default-content.svg';
</script>

<template>
  <div class="chat-default-content">
    <SvgIcon :file="defaultContentIcon" :is-pointer="false" :size="67" />
  </div>
</template>

<style lang="scss" scoped>
.chat-default-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: #f5f6f8;
}
</style>
