{"name": "@amdox/web-template", "version": "5.5.5", "homepage": "https://vben.pro", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "apps/web-template"}, "license": "MIT", "author": {"name": "vben", "email": "<EMAIL>", "url": "https://github.com/anncwb"}, "type": "module", "scripts": {"build": "pnpm vite build --mode production", "build:analyze": "pnpm vite build --mode analyze", "dev": "pnpm vite --mode development", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "imports": {"#/*": "./src/*"}, "dependencies": {"@amdox/access": "workspace:*", "@amdox/common-ui": "workspace:*", "@amdox/constants": "workspace:*", "@amdox/hooks": "workspace:*", "@amdox/icons": "workspace:*", "@amdox/layouts": "workspace:*", "@amdox/locales": "workspace:*", "@amdox/plugins": "workspace:*", "@amdox/preferences": "workspace:*", "@amdox/request": "workspace:*", "@amdox/stores": "workspace:*", "@amdox/styles": "workspace:*", "@amdox/types": "workspace:*", "@amdox/utils": "workspace:*", "@vueuse/core": "catalog:", "dayjs": "catalog:", "element-plus": "catalog:", "pinia": "catalog:", "vue": "catalog:", "vue-router": "catalog:"}, "devDependencies": {"unplugin-element-plus": "catalog:"}}