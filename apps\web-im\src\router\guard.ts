import type { Router } from 'vue-router';

// import { preferences } from '@amdox/preferences';
// import { startProgress, stopProgress } from '@amdox/utils';

// /**
//  * 通用守卫配置
//  * @param router
//  */
// function setupCommonGuard(router: Router) {
//   // 记录已经加载的页面
//   const loadedPaths = new Set<string>();

//   router.beforeEach(async (to) => {
//     to.meta.loaded = loadedPaths.has(to.path);

//     // 页面加载进度条
//     if (!to.meta.loaded && preferences.transition.progress) {
//       startProgress();
//     }
//     return true;
//   });

//   router.afterEach((to) => {
//     // 记录页面是否加载,如果已经加载，后续的页面切换动画等效果不在重复执行

//     loadedPaths.add(to.path);

//     // 关闭页面加载进度条
//     if (preferences.transition.progress) {
//       stopProgress();
//     }
//   });
// }

/**
 * 项目守卫配置
 * @param router
 */
function createRouterGuard(_router: Router) {
  /** 通用 */
  // 暂时不需要进度条显示
  // setupCommonGuard(router);
}

export { createRouterGuard };
