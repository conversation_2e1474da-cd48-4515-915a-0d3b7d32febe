/**
 * 该文件可自行根据业务逻辑进行调整
 */
import type { RequestClientOptions } from '@amdox/request';

import { useAppConfig } from '@amdox/hooks';
import { preferences } from '@amdox/preferences';
import {
  authenticateResponseInterceptor,
  defaultResponseInterceptor,
  errorMessageResponseInterceptor,
  loadingRequestInterceptor,
  localeRequestInterceptor,
  RequestClient,
} from '@amdox/request';
import { useAccessStore } from '@amdox/stores';

import { ElLoading, ElMessage } from 'element-plus';

import { useAuthStore } from '#/store';

import { refreshTokenApi } from './core';

const { apiURL } = useAppConfig(import.meta.env, import.meta.env.PROD);

// 全局加载实例管理
let loadingInstance: any = null;
let loadingCount = 0;

// 加载动画控制
const loadingControl = {
  show: () => {
    loadingCount++;
    if (loadingCount === 1) {
      loadingInstance = ElLoading.service({
        lock: true,
        text: '加载中...',
        background: 'rgba(0, 0, 0, 0.7)',
      });
    }
  },
  hide: () => {
    loadingCount = Math.max(0, loadingCount - 1);
    if (loadingCount === 0 && loadingInstance) {
      loadingInstance.close();
      loadingInstance = null;
    }
  },
};

// 语言环境格式化函数
const formatLocaleForAcceptLanguage = (locale: string): string => {
  const localeMap: Record<string, string> = {
    'zh-CN': 'zh-CN,zh;q=0.9',
    'en-US': 'en-US,en;q=0.9',
    'ja-JP': 'ja-JP,ja;q=0.9',
    'ko-KR': 'ko-KR,ko;q=0.9',
    'fr-FR': 'fr-FR,fr;q=0.9',
    'de-DE': 'de-DE,de;q=0.9',
    'es-ES': 'es-ES,es;q=0.9',
    'it-IT': 'it-IT,it;q=0.9',
    'pt-PT': 'pt-PT,pt;q=0.9',
    'ru-RU': 'ru-RU,ru;q=0.9',
    'ar-SA': 'ar-SA,ar;q=0.9',
    'th-TH': 'th-TH,th;q=0.9',
    'vi-VN': 'vi-VN,vi;q=0.9',
    'id-ID': 'id-ID,id;q=0.9',
    'ms-MY': 'ms-MY,ms;q=0.9',
    'hi-IN': 'hi-IN,hi;q=0.9',
    'bn-BD': 'bn-BD,bn;q=0.9',
  };
  return localeMap[locale] || `${locale};q=0.9`;
};

function createRequestClient(baseURL: string, options?: RequestClientOptions) {
  const client = new RequestClient({
    ...options,
    baseURL,
  });

  /**
   * 重新认证逻辑
   */
  async function doReAuthenticate() {
    console.warn('Access token or refresh token is invalid or expired. ');
    const accessStore = useAccessStore();
    const authStore = useAuthStore();
    accessStore.setAccessToken(null);
    if (
      preferences.app.loginExpiredMode === 'modal' &&
      accessStore.isAccessChecked
    ) {
      accessStore.setLoginExpired(true);
    } else {
      await authStore.logout();
    }
  }

  /**
   * 刷新token逻辑
   */
  async function doRefreshToken() {
    const accessStore = useAccessStore();
    const resp = await refreshTokenApi();
    const newToken = resp.data;
    accessStore.setAccessToken(newToken);
    return newToken;
  }

  function formatToken(token: null | string) {
    return token ? `Bearer ${token}` : null;
  }

  // 1. 添加语言环境拦截器
  client.addRequestInterceptor(
    localeRequestInterceptor({
      getLocale: () => preferences.app.locale,
      formatLocale: formatLocaleForAcceptLanguage,
    }),
  );

  // 2. 添加认证请求头处理
  client.addRequestInterceptor({
    fulfilled: async (config) => {
      const accessStore = useAccessStore();

      // 如果没有跳过token验证，则添加认证头
      if (!(config as any).skipToken) {
        config.headers.Authorization = formatToken(accessStore.accessToken);
      }

      return config;
    },
  });

  // 3. 添加加载动画拦截器（可选功能）
  const loadingInterceptors = loadingRequestInterceptor({
    showLoading: loadingControl.show,
    hideLoading: loadingControl.hide,
  });

  client.addRequestInterceptor(loadingInterceptors.request);
  client.addResponseInterceptor(loadingInterceptors.response);

  // 4. 处理返回的响应数据格式
  client.addResponseInterceptor(
    defaultResponseInterceptor({
      codeField: 'code',
      dataField: 'data',
      successCode: 0,
    }),
  );

  // 5. token过期的处理，包含401认证错误、token刷新和特定token过期状态码处理
  client.addResponseInterceptor(
    authenticateResponseInterceptor({
      client,
      doReAuthenticate,
      doRefreshToken,
      enableRefreshToken: preferences.app.enableRefreshToken,
      formatToken,
      onTokenExpired: async () => {
        // 处理特定的token过期情况（如424状态码）
        ElMessage.error('登录已过期，请重新登录');
        const accessStore = useAccessStore();
        const authStore = useAuthStore();
        accessStore.setAccessToken(null);
        if (
          preferences.app.loginExpiredMode === 'modal' &&
          accessStore.isAccessChecked
        ) {
          accessStore.setLoginExpired(true);
        } else {
          await authStore.logout();
        }
      },
      tokenExpiredCode: 424, // 处理424 token过期状态码
    }),
  );

  // 6. 通用的错误处理，包含HTTP错误、业务错误和旧系统错误处理
  client.addResponseInterceptor(
    errorMessageResponseInterceptor({
      makeErrorMessage: (msg: string, error: any) => {
        // 这里可以根据业务进行定制,你可以拿到 error 内的信息进行定制化处理，根据不同的 code 做不同的提示，而不是直接使用 message.error 提示 msg
        // 当前mock接口返回的错误字段是 error 或者 message
        const responseData = error?.response?.data ?? {};
        const errorMessage = responseData?.error ?? responseData?.message ?? '';
        // 如果没有错误信息，则会根据状态码进行提示
        ElMessage.error(errorMessage || msg);
      },
      onBusinessError: (message: string, error: any) => {
        // 处理业务逻辑错误
        ElMessage.error(message);
        console.error('业务错误:', error);
      },
      businessErrorCode: 1, // 业务错误码，根据实际项目调整
      oldSystemTokenExpiredState: '106', // 旧系统token过期状态
      oldSystemErrorStates: ['2', '3'], // 旧系统错误状态列表
    }),
  );

  return client;
}

/**
 * 默认的请求客户端实例
 * 返回解析后的数据（data字段）
 */
export const requestClient = createRequestClient(apiURL, {
  responseReturn: 'data',
});

/**
 * 基础的请求客户端实例
 * 返回原始的响应对象
 */
export const baseRequestClient = new RequestClient({ baseURL: apiURL });

/**
 * 创建自定义的请求客户端
 *
 * @param baseURL 基础URL
 * @param options 请求选项
 * @returns RequestClient实例
 *
 * @example
 * // 创建一个用于第三方API的客户端
 * const thirdPartyClient = createCustomRequestClient('https://api.example.com', {
 *   responseReturn: 'body',
 *   timeout: 30000,
 * });
 *
 * // 使用时
 * const data = await thirdPartyClient.get('/users', { skipToken: true });
 */
export function createCustomRequestClient(
  baseURL: string,
  options?: RequestClientOptions,
) {
  return createRequestClient(baseURL, options);
}

/**
 * 请求配置的扩展选项
 */
export interface ExtendedRequestConfig {
  /** 是否显示加载动画 */
  loading?: boolean;
  /** 是否跳过token验证 */
  skipToken?: boolean;
}

/**
 * 常用的请求方法封装
 */
export const api = {
  /**
   * GET请求
   */
  get: <T = any>(url: string, config?: ExtendedRequestConfig) =>
    requestClient.get<T>(url, config),

  /**
   * POST请求
   */
  post: <T = any>(url: string, data?: any, config?: ExtendedRequestConfig) =>
    requestClient.post<T>(url, data, config),

  /**
   * PUT请求
   */
  put: <T = any>(url: string, data?: any, config?: ExtendedRequestConfig) =>
    requestClient.put<T>(url, data, config),

  /**
   * DELETE请求
   */
  delete: <T = any>(url: string, config?: ExtendedRequestConfig) =>
    requestClient.delete<T>(url, config),

  /**
   * 上传文件
   */
  upload: requestClient.upload,

  /**
   * 下载文件
   */
  download: requestClient.download,
};
