# 行政班列表

## 接口信息

- **请求方法**：GET
- **接口地址**：`https://test-im.amdox.com.cn/im/group/management/getAdministrativeClassList`

#### 请求参数

| 字段     | 类型   | 是否必传 | 描述   |
| -------- | ------ | -------- | ------ |
| campusId | String | 是       | 校区ID |

#### 请求示例

```plaintext
GET /im/group/management/getAdministrativeClassList?campusId=10000169
```

#### 响应参数

| 字段      | 类型   | 是否必传 | 描述     |
| --------- | ------ | -------- | -------- |
| clazzId   | String | 是       | 班级ID   |
| clazzName | String | 是       | 班级名称 |
| schoolId  | String | 是       | 学校ID   |

#### 响应示例

```json
[
  {
    "clazzId": "10001451",
    "clazzName": "博学多才铸就未来的精英领航1班",
    "schoolId": ""
  },
  {
    "clazzId": "10001182",
    "clazzName": "一年级1班",
    "schoolId": ""
  },
  {
    "clazzId": "10001150",
    "clazzName": "一年级2班",
    "schoolId": ""
  }
]
```
