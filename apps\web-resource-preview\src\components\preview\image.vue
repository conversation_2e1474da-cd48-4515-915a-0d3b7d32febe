<script lang="ts" setup>
import { useHiddenLoading } from '#/hooks/useLoading';

// 定义组件名称
defineOptions({ name: 'ImagePreview' });

// 定义 props
defineProps<{ src: string }>();
const emits = defineEmits<{
  (e: 'loading'): void;
}>();

const { handleHiddenLoading } = useHiddenLoading(emits);

function loadImage() {
  handleHiddenLoading();
}
</script>

<template>
  <div class="image-preview">
    <img :src="src" alt="Image Preview" :onload="loadImage" />
  </div>
</template>

<style lang="scss" scoped>
.image-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;

  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain; // 保持图片宽高比，确保图片完整显示在容器内
  }
}
</style>
